# Environment Variables Management Strategy

This document outlines the strategy for managing environment variables and sensitive configurations for the Playlist Intelligence Agent MVP. It's crucial that secrets are not hardcoded or committed to the repository.

## Configuration Loading
Application configuration, including secrets and operational parameters, is primarily managed via environment variables. The backend application (Python/FastAPI) utilizes Pydantic Settings (`backend/src/app/config.py`) to load these variables from the operating system's environment or from `.env` files for local development.

## Local Development
For local development, environment variables are defined in `.env` files:
* **Root `.env`**: Contains Docker Compose variables (database configuration)
* **Backend `.env`**: Contains backend-specific variables (located in `backend/.env`)
* These `.env` files **MUST be included in the project's `.gitignore` file** to prevent accidental commitment of secrets.
* `.env.example` files **ARE committed** to the repository as templates for developers.

## Production Deployment
In production environments, environment variables and secrets **MUST be configured securely** through the hosting platform's provided mechanisms:
* **Backend**: Set secure environment variables in the hosting platform's service settings
* **Frontend**: Set build-time environment variables in the deployment platform's project settings

## Environment Variable Reference

### Root Environment Variables (`.env.example`)
Used primarily for Docker Compose configuration:

```bash
# Database Configuration for Docker Compose
DB_USER=admin
DB_PASSWORD=password
DB_NAME=playlist_agent_dev
DB_PORT=5432
DB_HOST=localhost  # Use 'db' if running app inside docker, 'localhost' if outside
```

### Backend Environment Variables (`backend/.env.example`)

#### Application Configuration
```bash
APP_TITLE="Playlist Intelligence Agent MVP - Dev"
API_VERSION="0.1.0"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
FRONTEND_URL="http://localhost:5173"  # Default Vite dev port
APP_ENV="development"  # or "production" - used for cookie settings
```

#### Database Configuration
```bash
DB_USER=admin
DB_PASSWORD=password
DB_HOST=localhost  # Use 'db' if running backend inside docker, 'localhost' if outside
DB_PORT=5432
DB_NAME=playlist_agent_dev
DB_ECHO=false  # Set to true to see SQLAlchemy SQL logs
```

#### Spotify Integration
```bash
SPOTIFY_CLIENT_ID="your_spotify_client_id_here"
SPOTIFY_CLIENT_SECRET="your_spotify_client_secret_here"
SPOTIFY_REDIRECT_URI="http://127.0.0.1:8000/api/v1/auth/callback"
```

#### Security Configuration
```bash
# Generate using: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
TOKENS_ENCRYPTION_KEY="your_fernet_encryption_key_here"

# Generate using: python -c "import secrets; print(secrets.token_hex(32))"
JWT_SECRET_KEY="your_jwt_secret_key_here"
JWT_ALGORITHM="HS256"
JWT_EXPIRY_MINUTES=60  # Example: 1 hour
```

## Security Best Practices

### Required Secrets
The following secrets **MUST** be configured as environment variables:
* **Spotify OAuth**: `SPOTIFY_CLIENT_ID`, `SPOTIFY_CLIENT_SECRET`
* **Database**: `DB_USER`, `DB_PASSWORD` (in production)
* **Encryption**: `TOKENS_ENCRYPTION_KEY` for encrypting Spotify tokens at rest
* **JWT**: `JWT_SECRET_KEY` for signing application JWTs
* **Future integrations**: LLM API keys when implemented

### Key Generation
Use the provided commands in the `.env.example` files to generate secure keys:
* **Fernet encryption key**: `python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"`
* **JWT secret key**: `python -c "import secrets; print(secrets.token_hex(32))"`

### Environment-Specific Configuration
* **Development**: Use `.env` files with placeholder values
* **Production**: Use hosting platform's secure environment variable management
* **Never commit**: Real secrets, API keys, or production credentials to version control

This strategy ensures that sensitive information is kept out of the codebase and can be managed appropriately for different environments.