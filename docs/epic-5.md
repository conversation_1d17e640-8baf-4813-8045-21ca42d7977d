# Epic 5: Curation Web Dashboard & Playlist Update Orchestration

**Goal:** To develop the interactive web-based user interface allowing the user to:
* View and act upon (approve/reject) new song recommendations in the "Discovery Queue." [user_input_file_1 source: 1137]
* View and act upon (approve/keep) removal suggestions in the "Removal Review Queue." [user_input_file_1 source: 1138]
* Manually trigger the full synchronization process (DNA update, discovery, suggestions). [user_input_file_1 source: 1139]
* Initiate the update of their Spotify playlist with all approved changes. [user_input_file_1 source: 1140]

## Story 5.1: Basic Frontend Application Setup & Main Layout (Finalized with Revisions)
**User Story:** As a User, I want a basic but functional web application initialized with the chosen frontend technology (e.g., Vue.js/Svelte, as per research/tech assumptions), incorporating the primary dark theme, a responsive layout shell (e.g., header, main content area), and rudimentary navigation, so I have a clear and consistent framework to access all dashboard features [user_input_file_1 source: 1141].
**Acceptance Criteria (ACs):**
1. A new frontend application project `shall` be initialized using a modern web framework (e.g., Vue.js or Svelte, with the final choice to be confirmed by the Architect/Design Architect based on research) within the frontend/ directory of the monorepo (as defined in Story 1.1) [user_input_file_1 source: 1142].
2. The frontend application `shall` implement a basic, responsive main layout structure [user_input_file_1 source: 1143].
3. This `shall` include a conceptual header area (e.g., for application title/logo), a primary content area where different views will be rendered, and a simple, persistent navigation mechanism (e.g., sidebar or top navigation bar links/icons) [user_input_file_1 source: 1144].
4. The navigation mechanism `shall` provide access to the core views defined in PRD Section 4c: Overview/Dashboard, Discovery Queue, Removal Queue, Settings, and Debugging [user_input_file_1 source: 1145].
5. The application `shall` implement the preferred dark grey theme with good text contrast as its default and primary visual styling, ensuring it is easy on the eyes (as per PRD Section 4e) [user_input_file_1 source: 1146].
6. The basic frontend application shell (including layout and navigation) `shall` be runnable locally using a standard development server command (e.g., npm run dev or similar, as provided by the chosen framework) [user_input_file_1 source: 1147].
7. Placeholder components or simple text indicators `shall` be rendered for each core view (AC3) when navigated to, confirming that the routing and view-switching mechanism is functional [user_input_file_1 source: 1148].
8. The main layout `shall` be designed using responsive principles to ensure it is reasonably usable on desktop, tablet, and mobile browser viewport sizes (as per PRD Section 4f) [user_input_file_1 source: 1149].
9. A basic 'Page Not Found' or '404 Error' view `shall` be implemented and displayed if the user attempts to navigate to an undefined route within the application [user_input_file_1 source: 1150].

## Story 5.2: Initial Playlist Configuration via UI (Finalized with Revisions)
**User Story:** As a User, if no playlist is currently configured for management (e.g., on first use or after a reset), I want to be guided to view a list of my Spotify playlists and select one for the Playlist Intelligence Agent to manage, with this choice being persistently saved by the system, so all subsequent system operations target the correct playlist [user_input_file_1 source: 1151].
**Acceptance Criteria (ACs):**
1. Upon initial application load by an authenticated user (Spotify auth from Epic 1), if the backend indicates that no managed playlist ID is currently configured for that user, the frontend `shall` display an interface specifically prompting the user to select a playlist for the agent to manage [user_input_file_1 source: 1152].
2. This "playlist selection" interface `shall` trigger a backend API call (to the endpoint established in Story 1.5 AC1) to fetch the list of the authenticated user's Spotify playlists (displaying at least playlist name and ID) [user_input_file_1 source: 1153].
3. While the list of Spotify playlists is being fetched from the backend, the UI `shall` display a loading indicator to the user [user_input_file_1 source: 1154].
4. The UI `shall` present this list of playlists to the user in a clear and selectable format (e.g., a searchable dropdown, a scrollable list) [user_input_file_1 source: 1155].
5. If no playlists are returned by the backend or if the fetch operation fails, an appropriate message `shall` be displayed to the user (e.g., 'No Spotify playlists found on your account.' or 'Could not retrieve your playlists at this time. Please ensure you are connected to Spotify via Settings and try again.') [user_input_file_1 source: 1156].
6. The user `shall` be able to select one playlist from this list via a UI element [user_input_file_1 source: 1157].
7. Upon selection, the frontend `shall` send the chosen Spotify Playlist ID to a dedicated backend API endpoint for configuration [user_input_file_1 source: 1158].
8. The backend `shall` implement an endpoint to receive and securely store this selected Spotify Playlist ID as the "currently managed playlist" for the authenticated user (e.g., in a user preferences table or similar in the database) [user_input_file_1 source: 1159].
9. After successful selection and confirmation of storage from the backend, the frontend `shall` navigate the user to the main Overview/Dashboard screen (Story 5.3), which should now operate in the context of this newly selected playlist [user_input_file_1 source: 1160].
10. If a managed playlist ID is already configured for the user, the application `shall` bypass this initial selection interface and proceed directly to load the Overview/Dashboard screen (Story 5.3) reflecting the configured playlist [user_input_file_1 source: 1161].
11. The system `shall` provide clear visual feedback to the user throughout the playlist selection and configuration process, indicating success or any errors encountered [user_input_file_1 source: 1162].

## Story 5.3: Implement Overview Dashboard with Sync Trigger, Queue Status & Active Playlist Display (Finalized with Revisions)
**User Story:** As a User, I want an Overview/Dashboard screen that clearly displays the name of the currently managed playlist, the current status of my review queues (e.g., "X songs pending in Discovery," "Y songs pending in Removal"), and allows me to manually trigger a full "Sync Now" process (initiating Playlist DNA update, new discovery, and removal suggestions via a backend API call), so I can control when the system refreshes its suggestions and understand the state of my queues [user_input_file_1 source: 1163].
**Acceptance Criteria (ACs):**
1. An "Overview/Dashboard" screen `shall` be implemented as a primary view in the frontend application, accessible via the main navigation [user_input_file_1 source: 1164].
2. This screen `shall` prominently and clearly display the name of the Spotify playlist currently configured for management by the agent [user_input_file_1 source: 1165].
3. This information `shall` be fetched from a backend API endpoint if not already available to the frontend [user_input_file_1 source: 1166].
4. If no playlist is configured, the screen `shall` guide the user to the selection/configuration process (Story 5.2) [user_input_file_1 source: 1167].
5. The screen `shall` display the current number of items pending review in the "Discovery Queue" and the "Removal Review Queue" [user_input_file_1 source: 1168].
6. These counts `shall` be fetched from dedicated backend API endpoints [user_input_file_1 source: 1169].
7. If queue counts (from AC3) cannot be fetched or are zero, the UI `shall` clearly display '0' or '-' for each respective queue count [user_input_file_1 source: 1170].
8. A clearly identifiable and easily accessible "Sync Now" button (or similar UI element) `shall` be present on this screen (as per PRD Section 4b) [user_input_file_1 source: 1171].
9. Activating the "Sync Now" button `shall` trigger a backend API call designed to initiate the full, asynchronous synchronization process (which encompasses Playlist DNA update, new release discovery, and removal suggestion generation, as detailed in Epics 2, 3, and 4) [user_input_file_1 source: 1172].
10. The UI `shall` provide immediate feedback to the user when the 'Sync Now' process is initiated (e.g., by disabling the 'Sync Now' button and showing a global loading indicator or status message like 'Sync in progress...') [user_input_file_1 source: 1173].
11. Upon receiving confirmation from the backend that the sync process has completed (or after a reasonable fixed delay post-initiation if the backend sync is fully asynchronous and initial MVP doesn't support real-time completion signals), the queue status counts (AC3) displayed on the dashboard `shall` be refreshed by re-fetching them from the backend [user_input_file_1 source: 1174].
12. The system `shall` implement basic error handling and provide user-friendly feedback for the "Sync Now" operation (e.g., displaying an error message if the sync fails to initiate or if the backend reports a critical error during its process) [user_input_file_1 source: 1175].

## Story 5.4: Display Discovery Queue & Implement Recommendation Review (Finalized with Revisions)
**User Story:** As a User, I want to view my pending new song recommendations one at a time in the "Discovery Queue" on the web dashboard, complete with track details, an embedded Spotify preview, the AI-generated justification, and be able to efficiently "approve" or "reject" each recommendation using the preferred swipe-style interface (with accessible button alternatives) [user_input_file_1 source: 1176].
**Acceptance Criteria (ACs):**
1. A "Discovery Queue" view `shall` be implemented in the frontend application, accessible via the main navigation [user_input_file_1 source: 1177].
2. This view `shall` fetch the list of "pending_review" song recommendations for the currently managed playlist from a backend API endpoint (consuming data stored in Story 3.5) [user_input_file_1 source: 1178].
3. While fetching the list of pending recommendations from the backend, the UI `shall` display a loading indicator within the Discovery Queue view [user_input_file_1 source: 1179].
4. Recommendations `shall` be presented to the user one song at a time, ordered with the newest discoveries (based on discovered_at timestamp from Story 3.5) appearing first [user_input_file_1 source: 1180].
5. For each song presented in the queue, the UI `shall` clearly display:
    * Track metadata (e.g., song name, primary artist(s) names, album name) [user_input_file_1 source: 1181].
    * An embedded Spotify play button/widget for song preview (utilizing Spotify Web Playback SDK or iframe embed, as per PRD Section 6 and research) [user_input_file_1 source: 1182].
    * The AI-generated justification for the recommendation (retrieved from the backend) [user_input_file_1 source: 1183].
6. The user `shall` be able to interact with the presented song using a swipe-style interface (e.g., swipe right for "approve," swipe left for "reject") as the primary interaction method [user_input_file_1 source: 1184].
7. Clear, easily accessible alternative controls (e.g., distinct "Approve" and "Reject" buttons) `shall` be provided for each song, usable via mouse click or keyboard interaction, ensuring accessibility (as per PRD Section 4b & 4d) [user_input_file_1 source: 1185].
8. When the user "approves" a recommendation, the frontend `shall` send this decision (including the track ID and the new status "approved_for_addition") to a backend API endpoint [user_input_file_1 source: 1186].
9. The backend `shall` update the recommendation's status in the database [user_input_file_1 source: 1187].
10. When the user "rejects" a recommendation, the frontend `shall` send this decision (including the track ID and the new status "rejected") to a backend API endpoint [user_input_file_1 source: 1188].
11. The backend `shall` update the recommendation's status in the database [user_input_file_1 source: 1189].
12. After an action (approve/reject) is successfully communicated to the backend, the current song `shall` be removed from the frontend queue view, and the next pending recommendation (if any) `shall` be presented automatically [user_input_file_1 source: 1190].
13. The UI `shall` provide clear visual feedback confirming the user's action upon successful communication of the approve/reject decision to the backend, or an appropriate error message if the backend API call for this action fails [user_input_file_1 source: 1191].
14. If the Discovery Queue is empty, a clear message indicating "No new recommendations to review at this time" `shall` be displayed [user_input_file_1 source: 1192].
15. If fetching recommendations from the backend fails, an appropriate error message `shall` be displayed to the user in the Discovery Queue view (e.g., 'Could not load recommendations. Please try again later or trigger a new sync.') [user_input_file_1 source: 1193].

## Story 5.5: Display Removal Review Queue & Implement Suggestion Review (Finalized with Revisions)
**User Story:** As a User, I want to view my pending song removal suggestions one at a time in the "Removal Review Queue" on the web dashboard, complete with track details, an embedded Spotify preview, the AI-generated justification, and be able to efficiently "approve removal" or "keep" each song using the preferred swipe-style interface (with accessible button alternatives) [user_input_file_1 source: 1194].
**Acceptance Criteria (ACs):**
1. A "Removal Review Queue" view `shall` be implemented in the frontend application, accessible via the main navigation [user_input_file_1 source: 1195].
2. This view `shall` fetch the list of "pending_review" song removal suggestions for the managed playlist from a backend API endpoint (consuming data stored in Story 4.4) [user_input_file_1 source: 1196].
3. Removal suggestions `shall` be presented to the user one song at a time, ordered by the date they were suggested (newest first) as the default for MVP [user_input_file_1 source: 1197].
4. While fetching the list of pending removal suggestions from the backend, the UI `shall` display a loading indicator within the Removal Review Queue view [user_input_file_1 source: 1198].
5. For each song presented in this queue, the UI `shall` clearly display:
    * Track metadata (e.g., song name, primary artist(s) names, album name) [user_input_file_1 source: 1199].
    * An embedded Spotify play button/widget for song preview [user_input_file_1 source: 1200].
    * The AI-generated justification for the removal suggestion (retrieved from the backend) [user_input_file_1 source: 1201].
    * The primary reason for the suggestion (e.g., "Vibe Outlier," "Exceeds Release Age Preference") [user_input_file_1 source: 1202].
6. The user `shall` be able to interact with the presented song using a swipe-style interface (e.g., swipe right to "approve removal," swipe left to "keep") as the primary interaction method [user_input_file_1 source: 1203]. (Note: UI/UX Spec Flow 4.4 has swipe right = Keep, swipe left = Remove. This needs to be consistent.)
7. Clear, easily accessible alternative controls (e.g., distinct "Approve Removal" and "Keep Song" buttons) `shall` be provided for each song, usable via mouse click or keyboard interaction [user_input_file_1 source: 1204].
8. When the user "approves removal" for a song, the frontend `shall` send this decision (including the track ID and the new status "remove_approved") to a backend API endpoint [user_input_file_1 source: 1205].
9. The backend `shall` update the suggestion's status in the database [user_input_file_1 source: 1206].
10. When the user chooses to "keep" a song, the frontend `shall` send this decision (including the track ID and the new status "kept_by_user") to a backend API endpoint [user_input_file_1 source: 1207].
11. The backend `shall` update the suggestion's status in the database [user_input_file_1 source: 1208].
12. After an action is successfully communicated to the backend, the current song `shall` be removed from this queue view, and the next pending removal suggestion (if any) `shall` be presented automatically [user_input_file_1 source: 1209].
13. The UI `shall` provide clear visual feedback confirming the user's action upon successful communication of the approve removal/keep decision to the backend, or an appropriate error message if the backend API call for this action fails [user_input_file_1 source: 1210].
14. If the Removal Review Queue is empty, a clear message indicating "No songs currently suggested for removal" `shall` be displayed [user_input_file_1 source: 1211].
15. If fetching removal suggestions from the backend fails, an appropriate error message `shall` be displayed to the user in the Removal Review Queue view [user_input_file_1 source: 1212].

## Story 5.6: Implement "Apply Approved Changes to Spotify" Functionality (Finalized with Revisions)
**User Story:** As a User, after I have reviewed songs in the queues, I want a clear and explicit way (e.g., an "Apply Changes" button) to instruct the system to take all my approved additions and approved removals and execute these changes on my actual Spotify playlist via a backend API call, with feedback on the success or failure of the update [user_input_file_1 source: 1213].
**Acceptance Criteria (ACs):**
1. A clear and explicit UI element (e.g., an "Apply Changes to Spotify" button) `shall` be available to the user, logically placed (e.g., on the Overview/Dashboard screen or persistently visible if appropriate) [user_input_file_1 source: 1214].
2. Before executing changes, activating this button `shall` first present a confirmation dialog to the user [user_input_file_1 source: 1215].
3. This dialog `shall` summarize the total number of songs approved for addition and the total number of songs approved for removal [user_input_file_1 source: 1216].
4. While the backend is processing the 'Apply Changes' request (after user confirmation from AC2), the UI `shall` clearly indicate that changes are in progress (e.g., by disabling the 'Apply Changes' button and/or showing a persistent global loading indicator/message) and prevent further 'Apply Changes' submissions until a final status (success/failure with details) is received from the backend [user_input_file_1 source: 1217].
5. Upon user confirmation in the dialog, the frontend `shall` make a single backend API call designed to trigger the execution of all currently approved playlist modifications [user_input_file_1 source: 1218].
6. The backend API endpoint for this action `shall`:
    * Retrieve all recommendations for the managed playlist with status "approved_for_addition" [user_input_file_1 source: 1219].
    * Retrieve all removal suggestions for the managed playlist with status "remove_approved" [user_input_file_1 source: 1220].
    * Make the necessary calls to the Spotify API to add the approved tracks to the user's Spotify playlist [user_input_file_1 source: 1221].
    * Make the necessary calls to the Spotify API to remove the approved-for-removal tracks from the user's Spotify playlist [user_input_file_1 source: 1222].
7. The backend `shall` update the status of the processed recommendations and removal suggestions in the local database (e.g., to "applied_to_spotify," "failed_to_apply_to_spotify") after attempting Spotify API operations [user_input_file_1 source: 1223].
8. The frontend `shall` provide comprehensive feedback to the user regarding the outcome of the "Apply Changes" operation, including:
    * A general success or failure message [user_input_file_1 source: 1224].
    * Counts of songs successfully added and successfully removed [user_input_file_1 source: 1225].
    * If specific additions/removals fail during the Spotify API interaction, the backend `should` attempt to complete as many changes as possible and report specific failures back to the frontend, which then `shall` display a summary of these specific successes and any individual failures to the user [user_input_file_1 source: 1226].
9. The "Apply Changes to Spotify" button `shall` be disabled or provide appropriate visual feedback if there are no pending approved changes (i.e., no songs marked "approved_for_addition" or "remove_approved") to apply [user_input_file_1 source: 1227].

## Story 5.7: Implement Configuration/Settings Screen (MVP) (Finalized with Revisions)
**User Story:** As a User (and technical owner of this personal application), I want a simple Configuration/Settings screen where I can view the status of my Spotify connection and see the ID/name of the currently managed playlist [user_input_file_1 source: 1228]. (For MVP, changing the playlist here might be deferred if initial selection in 5.2 is robust, but viewing is key).
**Acceptance Criteria (ACs):**
1. A "Settings" or "Configuration" view `shall` be implemented in the frontend application, accessible via the main navigation structure established in Story 5.1 [user_input_file_1 source: 1229].
2. This screen `shall` display the Spotify username or ID associated with the currently authenticated Spotify account (information fetched from a backend endpoint or stored client-side after auth) [user_input_file_1 source: 1230].
3. This screen `shall` clearly display the name and Spotify ID of the Spotify playlist that is currently configured for management by the Playlist Intelligence Agent (as set up in Story 5.2) [user_input_file_1 source: 1231].
4. The screen `shall` indicate the current status of the Spotify API connection (e.g., "Connected & Token Valid," "Token Expired - Re-authentication Recommended," "Not Connected") [user_input_file_1 source: 1232].
5. This status `shall` be determined by querying a backend endpoint or interpreting stored token information [user_input_file_1 source: 1233].
6. A button or link `shall` be provided on this screen that allows the user to re-initiate the Spotify authentication flow (as defined in Story 1.4) [user_input_file_1 source: 1234].
7. Activating this `shall` clearly inform the user they are about to re-authenticate with Spotify, and then trigger the flow [user_input_file_1 source: 1235].
8. Feedback on the success or failure of any re-authentication attempt `shall` be provided on this screen, and the connection status (AC4) `shall` update accordingly [user_input_file_1 source: 1236].
9. (Out of Scope for MVP Story 5.7) The UI for changing the currently managed playlist to a different one from the user's Spotify account [user_input_file_1 source: 1237].
10. (Out of Scope for MVP Story 5.7) The UI for managing other detailed application operational parameters (e.g., similarity thresholds, LLM justification length settings) [user_input_file_1 source: 1238]. These will remain system-defined defaults for MVP.

## Story 5.8: Implement Debugging Screen View (Finalized with Revisions)
**User Story:** As a User (and technical owner), I want a dedicated Debugging Screen in the web dashboard that can retrieve and display recent system errors, API token status, or a log of recent sync activities from the backend, so I can quickly diagnose operational issues if they arise [user_input_file_1 source: 1239].
**Acceptance Criteria (ACs):**
1. A "Debugging" view `shall` be implemented in the frontend application, accessible via the main navigation (as per Story 5.1) [user_input_file_1 source: 1240].
2. Its visibility or prominence may be secondary to core features [user_input_file_1 source: 1241].
3. This screen `shall` provide an interface element (e.g., a "Fetch Recent Errors" button) that, when activated, calls a backend API endpoint to retrieve and display a list of recent critical system errors or exceptions logged by the backend application [user_input_file_1 source: 1242].
4. The display should include at least a timestamp, error message, and basic context if available [user_input_file_1 source: 1243].
5. This screen `shall` clearly display the current status of the Spotify API access token (e.g., "Valid," "Expired," "Not Set") and, if the backend can provide it, an indication of when the access token is due to expire [user_input_file_1 source: 1244].
6. This screen `shall` provide an interface element (e.g., a "Fetch Sync Log" button) that, when activated, calls a backend API endpoint to retrieve and display a summary log of recent synchronization activities (e.g., last sync timestamp, duration, number of tracks processed, number of recommendations generated, number of removal suggestions generated) [user_input_file_1 source: 1245].
7. The information displayed on the Debugging Screen is intended for diagnostic purposes by a technical user (the primary user/developer) and does not require elaborate styling beyond clarity and readability [user_input_file_1 source: 1246].
8. Backend API endpoints `shall` be created to securely provide the data required by AC2, AC3 (token status details not already client-side), and AC4 [user_input_file_1 source: 1247].
9. While fetching data for display on the debugging screen (e.g., recent errors, sync log), appropriate loading indicators `shall` be displayed [user_input_file_1 source: 1248].
10. If data fetching from the backend fails for any section, a user-friendly error message (appropriate for a technical user context) `shall` be shown in place of that data [user_input_file_1 source: 1249].