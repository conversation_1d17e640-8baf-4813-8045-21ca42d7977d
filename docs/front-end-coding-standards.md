# Frontend Coding Standards

This document outlines specific coding standards for the frontend development of the Playlist Intelligence Agent MVP. For comprehensive project-wide coding standards, including detailed TypeScript, Vue.js, and Vite conventions, **refer primarily to the "TypeScript / Vue.js / Vite (Frontend) Specifics" subsection within `docs/operational-guidelines.md`**.

The following points reiterate or specifically emphasize conventions from the Frontend Architecture Document:

* **Framework & API Usage:**
    * Primarily leverage **Vue 3's Composition API** for building components.
    * Use **Composables** for extracting and reusing stateful logic.
* **Component Naming:**
    * **PascalCase for `.vue` files and component names** (e.g., `SongReviewCard.vue`, used as `<SongReviewCard />`).
* **Styling (Tailwind CSS):**
    * Prefer applying utility classes directly in templates.
    * For complex, reusable styles, create dedicated Vue components that encapsulate styles by composing Tailwind utilities.
    * Use `@apply` within `<style scoped>` for specific custom component classes judiciously.
* **File Structure:**
    * Adhere strictly to the layout defined in `docs/front-end-project-structure.md`.
* **State Management (Pinia):**
    * Follow conventions for store structure and async action patterns as detailed in `docs/front-end-state-management.md`.
* **API Interaction:**
    * Use the service pattern and the central `apiClient.ts` for backend communication, as detailed in `docs/front-end-api-interaction.md`.

Adherence to the detailed standards in `docs/operational-guidelines.md` and these frontend-specific emphases is mandatory.