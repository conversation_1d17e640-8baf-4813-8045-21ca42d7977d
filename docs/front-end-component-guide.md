# Frontend Component Guide

This document outlines the conventions for naming, organizing, and specifying UI components for the Playlist Intelligence Agent MVP. Detailed specifications for most feature-specific components will emerge as user stories are implemented.

## Component Naming & Organization

* **Component Naming Convention:** **PascalCase for files and component names (e.g., `UserProfileCard.vue`, imported and used as `<UserProfileCard />`)**. All component files MUST follow this convention.
* **Organization:** Globally reusable presentational components in `src/components/common/`. Layout components in `src/components/layout/`. Feature-specific components co-located within their feature directory (e.g., `src/features/reviewQueue/components/SongCard.vue`). Refer to `docs/front-end-project-structure.md`. The primary philosophy is to keep components feature-specific by default. A component should only be promoted to `src/components/common/` or `src/components/layout/` if it's truly generic, presentational, and demonstrably reusable across multiple distinct features or application layouts.

## Template for Component Specification

For each significant UI component identified from the UI/UX Specification and design files, the following details MUST be provided. This template serves as the standard. The AI agent MUST follow this template whenever a new component is identified for development.

### Component: `{ComponentName}` (e.g., `SongReviewCard`, `QueueStatusDisplay`)

* **Purpose:** {Briefly describe what this component does and its role in the UI. MUST be clear and concise.}
* **Source File(s):** {e.g., `src/features/reviewQueue/components/SongReviewCard.vue`. MUST be the exact path.}
* **Visual Reference:** {Link to specific Figma frame/component (if available from UI/UX Spec) or Storybook page. REQUIRED if visual design exists.}
* **Props (Properties):**
    { List each prop the component accepts. For each prop, all columns in the table MUST be filled. Types should be TypeScript types. }
    | Prop Name     | Type                                      | Required? | Default Value | Description                                                                                                   |
    | :------------ | :---------------------------------------- | :-------- | :------------ | :------------------------------------------------------------------------------------------------------------ |
    | `itemData`    | `RecommendationItemDto \| RemovalSuggestionItemDto` (from `src/types/apiTypes.ts`) | Yes       | N/A           | The data object for the song recommendation or removal suggestion to display. MUST conform to the API DTO. |
    | `queueType`   | `'discovery' \| 'removal'`                | Yes       | N/A           | Indicates if the card is for the discovery or removal queue, to tailor actions/text.                         |
    | `onAction`    | `(action: 'approve' \| 'reject' \| 'keep' \| 'remove_approved', itemId: string) => void` | Yes    | N/A           | Callback function when a review action is triggered. Payload MUST match expected signature.                  |
    | `onSkip`      | `(itemId: string) => void`                | No        | N/A           | Callback function when the 'Skip for Now' action is triggered (if applicable).                                |
* **Internal State (if any, using Vue Composition API `ref` or `reactive`):**
    { Describe any significant internal state. Only list state *not* derived from props or global Pinia state. }
    | State Variable      | Type      | Initial Value | Description                                                                                  |
    | :------------------ | :-------- | :------------ | :------------------------------------------------------------------------------------------- |
    | `isJustificationVisible` | `boolean` | `false`       | Controls the visibility of the AI-generated justification text. Toggled by user action.      |
    | `isPreviewLoading`  | `boolean` | `true`        | Tracks loading state of the embedded Spotify preview.                                        |
* **Key UI Elements / Structure (Conceptual Vue Template using `<script setup lang="ts">`):**
    { Provide a pseudo-Vue template structure. **This structure dictates the primary output for the AI agent.** }
    ```vue
    <template>
      <div class="song-review-card bg-gray-700 p-4 rounded-lg shadow-md"> <img :src="itemData.albumArtUrl || defaultPlaceholder" alt="Album Art" class="w-full h-48 object-cover rounded" />
        <h3 class="text-lg font-semibold mt-2">{{ itemData.trackName }}</h3>
        <p class="text-sm text-gray-400">{{ itemData.artists.map(a => a.name).join(', ') }} - {{ itemData.albumName }}</p>
        <div v-if="itemData.previewUrl" class="mt-2">
          </div>
        <button @click="isJustificationVisible = !isJustificationVisible" class="text-xs text-blue-400 mt-1">
          {{ isJustificationVisible ? 'Hide' : 'Show' }} Reason
        </button>
        <p v-if="isJustificationVisible" class="text-xs mt-1">{{ itemData.justification }}</p>
        <div class="mt-4 flex justify-around">
          <button v-if="queueType === 'discovery'" @click="onAction('reject', itemData.id)" class="bg-red-500 p-2 rounded">Reject</button>
          <button v-if="queueType === 'discovery'" @click="onAction('approve', itemData.id)" class="bg-green-500 p-2 rounded">Approve</button>
          <button v-if="queueType === 'removal'" @click="onAction('remove_approved', itemData.id)" class="bg-red-500 p-2 rounded">Remove</button>
          <button v-if="queueType === 'removal'" @click="onAction('keep', itemData.id)" class="bg-green-500 p-2 rounded">Keep</button>
        </div>
      </div>
    </template>

    <script setup lang="ts">
    import { ref } from 'vue';
    // import type { RecommendationItemDto, RemovalSuggestionItemDto } from '@/types/apiTypes'; // Assuming types are defined

    interface Props {
      itemData: any; // Replace 'any' with actual RecommendationItemDto | RemovalSuggestionItemDto
      queueType: 'discovery' | 'removal';
      onAction: (action: 'approve' | 'reject' | 'keep' | 'remove_approved', itemId: string) => void;
      onSkip?: (itemId: string) => void;
    }
    const props = defineProps<Props>();

    const isJustificationVisible = ref(false);
    // const isPreviewLoading = ref(true); // If Spotify embed has loading states
    const defaultPlaceholder = './path/to/default-album-art.png'; // Define a default placeholder
    </script>
    ```
* **Events Emitted (`defineEmits`):**
    * (Usually handled via prop functions like `onAction`, `onSkip` in Composition API unless a more generic event emission is needed.)
* **Actions Triggered (Side Effects via Pinia stores or services):**
    * None directly within this presentational component. Parent view/container handles calling `onAction`/`onSkip` which then trigger store actions or service calls.
* **Styling Notes:**
    * Primarily uses **Tailwind CSS utility classes** as shown in the conceptual template. Key classes: `bg-gray-700`, `p-4`, `rounded-lg`, `shadow-md`, `text-lg`, `font-semibold`, `text-sm`, `text-gray-400`, `text-blue-400`, `bg-red-500`, `bg-green-500`. Dynamic styling based on props or state (e.g., for swipe animations) will be handled via Vue's class bindings.
* **Accessibility Notes:**
    * MUST ensure all interactive elements (buttons, toggles, embed) are keyboard focusable and operable.
    * Button for justification toggle: `aria-expanded` should reflect `isJustificationVisible`. The paragraph with justification should have an `id` linked by `aria-controls`.
    * Action buttons MUST have clear, descriptive `aria-label` attributes if icons are used or text is not sufficiently descriptive (e.g., `aria-label="Approve song for addition"`).
    * The card itself could have `role="article"` and `aria-labelledby` pointing to the track name.

*(Conceptual components listed in UI/UX Spec Section 6, like `MainAppLayout`, `QueueLinkCard`, `SongReviewCard`, `PrimaryActionButton`, `ModalDialog`, etc., will follow this specification template when detailed for development.)*