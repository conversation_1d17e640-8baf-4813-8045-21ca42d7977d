# Core Workflow / Sequence Diagrams

## Manual Sync Process Flow
```mermaid
sequenceDiagram
    actor User
    participant FE as Frontend Web App
    participant API as Backend API (Controller)
    participant SyncOrch as SyncOrchestrationService
    participant DNASvc as PlaylistDNACalculationService
    participant NewRelSvc as NewReleaseDiscoveryService
    participant RemSv<PERSON> as RemovalSuggestionService
    participant SpotifySvc as SpotifyIntegrationService
    participant LLMSvc as LLMIntegrationService
    participant DB as PersistenceLayer (Database)

    %% Phase 1: Trigger Sync & Initial Setup
    User->>FE: Clicks "Refresh Discoveries"
    FE->>API: POST /api/v1/sync/trigger
    API->>SyncOrch: initiateSyncTask(user)
    Note over API,SyncOrch: Generates syncId
    API-->>FE: 202 Accepted (syncId, message)
    FE-->>User: UI: Sync in progress (modal, disable button)

    loop Periodically Poll Status
        FE->>API: GET /api/v1/sync/status/{sync_id}
        API->>DB: Read sync_log_entries for syncId
        DB-->>API: Sync status, progressMessage
        API-->>FE: 200 OK (syncStatusResponseDto)
        FE-->>User: UI: Update sync progress (e.g., modal checkpoints)
    end

    activate SyncOrch
    SyncOrch->>DB: Create sync_log_entries (syncId, status: initiated)
    SyncOrch->>DB: Get managed_playlist_id for user
    DB-->>SyncOrch: managed_playlist_id
    SyncOrch->>DB: Update sync_log_entries (status: dna_analysis_started)
    deactivate SyncOrch

    %% Phase 2: Playlist DNA Update (Conditional)
    activate SyncOrch
    SyncOrch->>DNASvc: checkAndUpdateDna(managed_playlist_id)
    activate DNASvc
    DNASvc->>SpotifySvc: getCurrentSnapshotId(managed_playlist_id)
    SpotifySvc-->>DNASvc: currentSnapshotId
    DNASvc->>DB: getStoredSnapshotId(managed_playlist_id)
    DB-->>DNASvc: storedSnapshotId
    alt snapshot_id differs OR no DNA exists
        DNASvc->>SyncOrch: Update sync_log_entries (dna_recalculated: true) via DB directly or through SyncOrch
        DNASvc->>SpotifySvc: getFullPlaylistDetails(managed_playlist_id)
        SpotifySvc-->>DNASvc: Track details, features, artist/genre info
        Note over DNASvc: Computes new Playlist DNA (aggregates, vector)
        DNASvc->>DB: storeNewPlaylistDna(newDNA, currentSnapshotId)
    else snapshot_id is the same
        DNASvc->>SyncOrch: Update sync_log_entries (dna_recalculated: false) via DB directly or through SyncOrch
        Note over DNASvc: Skips DNA re-computation
    end
    DNASvc-->>SyncOrch: {status, currentPlaylistDna (or ID)}
    deactivate DNASvc
    deactivate SyncOrch

    %% Phase 3: New Release Discovery
    activate SyncOrch
    SyncOrch->>DB: Update sync_log_entries (status: discovery_started)
    SyncOrch->>NewRelSvc: findNewRecommendations(currentPlaylistDna)
    activate NewRelSvc
    Note over NewRelSvc: Uses PlaylistDNA & ConfigService
    NewRelSvc->>SpotifySvc: searchNewReleases(seeds, filters)
    SpotifySvc-->>NewRelSvc: Raw new releases
    NewRelSvc->>SpotifySvc: filterByDateAndAvailability(rawReleases)
    SpotifySvc-->>NewRelSvc: Filtered new tracks
    NewRelSvc->>SpotifySvc: getAudioFeatures(filteredTracks)
    SpotifySvc-->>NewRelSvc: Audio features for new tracks
    Note over NewRelSvc: Scores similarity against PlaylistDNA vector
    loop For tracks meeting similarity threshold
         NewRelSvc->>LLMSvc: generateJustification(playlistDnaSummary, trackFeatures)
         LLMSvc-->>NewRelSvc: Justification text
    end
    NewRelSvc->>DB: storePendingRecommendations(recommendations)
    Note over NewRelSvc: Logs recommendationsGenerated, llmCalls, llmTokens
    NewRelSvc-->>SyncOrch: {status}
    deactivate NewRelSvc
    deactivate SyncOrch

    %% Phase 4: Removal Suggestion Generation
    activate SyncOrch
    SyncOrch->>DB: Update sync_log_entries (status: suggestion_generation_started)
    SyncOrch->>RemSvc: findRemovalSuggestions(currentPlaylistDna)
    activate RemSvc
    RemSvc->>SpotifySvc: getCurrentPlaylistTracks(managed_playlist_id)
    SpotifySvc-->>RemSvc: Current playlist tracks
    Note over RemSvc: Calculates deviation from PlaylistDNA (vibe outliers)
    Note over RemSvc: Applies filtering/protection rules (using ConfigService)
    loop For valid removal candidates
        RemSvc->>LLMSvc: generateJustification(playlistDnaSummary, trackFeatures, reason)
        LLMSvc-->>RemSvc: Justification text
    end
    RemSvc->>DB: storePendingRemovalSuggestions(suggestions)
    Note over RemSvc: Logs removalsSuggested, updates llmCalls, llmTokens
    RemSvc-->>SyncOrch: {status}
    deactivate RemSvc
    deactivate SyncOrch

    %% Phase 5: Finalization & Notification
    activate SyncOrch
    SyncOrch->>DB: Update sync_log_entries (final status, completed_at, duration, metrics, errors)
    SyncOrch->>DB: Update managed_playlists (last_synced_at)
    deactivate SyncOrch
     
    Note over FE, API: Eventually, polling GET /api/v1/sync/status/{sync_id} reflects final status.
    FE->>API: GET /api/v1/sync/queues/status
    API->>DB: Read queue counts
    DB-->>API: {discoveryQueueCount, removalQueueCount}
    API-->>FE: 200 OK (queueStatusResponseDto)
    FE-->>User: UI: Sync complete (message), updates queue counts
```


## Review Queues & Action Flow (Example: Discovery Queue)
```mermaid
sequenceDiagram
    actor User
    participant FE as Frontend Web App
    participant API as Backend API (Controller)
    participant DB as PersistenceLayer (Database)

    %% User Navigates to Queue
    User->>FE: Navigates to Discovery Queue
    FE->>API: GET /api/v1/reviews/recommendations (with pagination params)
    activate API
    API->>DB: Query PendingRecommendations (status: 'pending_review', ordered, paginated)
    activate DB
    DB-->>API: List of recommendation items & totalPending
    deactivate DB
    API-->>FE: 200 OK (GetRecommendationsResponseDto)
    deactivate API

    %% Frontend Displays Items
    alt Queue is Empty
        FE-->>User: UI: Display "No new recommendations" message
    else Queue has Items
        FE-->>User: UI: Display first Recommendation Item (details, preview, justification)
    end

    %% User Actions an Item (Example: Approve)
    User->>FE: Actions item (e.g., Clicks "Approve")
    FE->>API: POST /api/v1/reviews/recommendations/{rec_id}/action (body: {"action": "approve_for_addition"})
    activate API
    API->>DB: Update PendingRecommendation status (rec_id, new_status: 'approved_for_addition')
    activate DB
    DB-->>API: Update confirmation
    deactivate DB
    API-->>FE: 200 OK (ActionResponseDto: {id, status, message})
    deactivate API

    %% Frontend Updates UI
    FE-->>User: UI: Show transient feedback (e.g., "Song Approved!")
    FE-->>User: UI: Remove actioned item, display next item or "empty queue" message
    
    Note over User, FE: User continues reviewing items until queue is empty or navigates away.
```


## Initial Playlist Configuration Flow
```mermaid
sequenceDiagram
    actor User
    participant FE as Frontend Web App
    participant API as Backend API (Controller)
    participant SpotifySvc as SpotifyIntegrationService
    participant DB as PersistenceLayer (Database)

    %% Initial State: User is authenticated, FE checks if playlist is configured
    FE->>API: GET /api/v1/playlists/managed 
    activate API
    API->>DB: Check for existing managed_playlist for user
    activate DB
    DB-->>API: {isConfigured: false, managedPlaylist: null} 
    deactivate DB
    API-->>FE: Response 
    deactivate API
    FE-->>User: UI: Prompts User to select a playlist

    FE->>API: GET /api/v1/playlists
    activate API
    API->>SpotifySvc: getUserPlaylistsFromSpotify()
    activate SpotifySvc
    Note over SpotifySvc: Interacts with Spotify API
    SpotifySvc-->>API: List of user's Spotify playlists
    deactivate SpotifySvc
    API-->>FE: 200 OK (UserPlaylistsResponseDto)
    deactivate API
    FE-->>User: UI: Displays list of Spotify playlists

    User->>FE: Selects a playlist (spotifyPlaylistId)
    
    FE->>API: POST /api/v1/playlists/managed (body: {spotifyPlaylistId})
    activate API
    Note over API: Validates spotifyPlaylistId
    API->>SpotifySvc: getPlaylistDetails(spotifyPlaylistId) 
    activate SpotifySvc
    SpotifySvc-->>API: Playlist details 
    deactivate SpotifySvc
    API->>DB: Store/Update managed_playlists record 
    activate DB
    DB-->>API: Confirmation 
    deactivate DB
    API-->>FE: 200 OK (SetManagedPlaylistResponseDto)
    deactivate API
    FE-->>User: UI: Shows success message, navigates to Dashboard
```


## Apply Approved Changes to Spotify Flow
```mermaid
sequenceDiagram
    actor User
    participant FE as Frontend Web App
    participant API as Backend API (Controller)
    participant ApplySvc as ApplyChangesService
    participant SpotifySvc as SpotifyIntegrationService
    participant DB as PersistenceLayer (Database)

    %% User Initiates Apply Changes
    User->>FE: Clicks "Apply Changes to Spotify"
    FE->>User: Show Confirmation Dialog (counts of additions/removals)
    User->>FE: Confirms action

    %% Frontend Triggers Backend Process
    FE->>API: POST /api/v1/playlists/managed/apply-changes
    API->>ApplySvc: initiateApplyChanges(user)
    Note over API,ApplySvc: Generates applyId
    API-->>FE: 202 Accepted (applyId, message)
    FE-->>User: UI: "Applying Changes..." (modal, disable button)

    %% Frontend Polls for Status
    loop Periodically Poll Status
        FE->>API: GET /api/v1/playlists/managed/apply-changes/status/{apply_id}
        API->>DB: Read apply_changes_job_status for applyId
        DB-->>API: Job status, summary
        API-->>FE: 200 OK (applyChangesStatusResponseDto)
        FE-->>User: UI: Update progress (e.g., modal checkpoints if available)
    end

    %% Backend Asynchronous Processing
    activate ApplySvc
    Note over ApplySvc: Logs initiation (e.g., to a job status table with applyId)
    
    %% Process Additions
    ApplySvc->>DB: Get PendingRecommendations (status: 'approved_for_addition')
    DB-->>ApplySvc: List of tracks to add
    alt Tracks to Add Exist
        ApplySvc->>SpotifySvc: addTracksToPlaylist(track_ids)
        activate SpotifySvc
        Note over SpotifySvc: Batched calls to Spotify API
        SpotifySvc-->>ApplySvc: {successes, failures_with_reasons}
        deactivate SpotifySvc
        loop For each track addition attempt
            ApplySvc->>DB: Update PendingRecommendation status (e.g., 'applied_to_spotify' or 'failed_to_apply')
        end
    end

    %% Process Removals
    ApplySvc->>DB: Get PendingRemovalSuggestions (status: 'remove_approved')
    DB-->>ApplySvc: List of tracks to remove
    alt Tracks to Remove Exist
        ApplySvc->>SpotifySvc: removeTracksFromPlaylist(track_ids)
        activate SpotifySvc
        Note over SpotifySvc: Batched calls to Spotify API
        SpotifySvc-->>ApplySvc: {successes, failures_with_reasons}
        deactivate SpotifySvc
        loop For each track removal attempt
            ApplySvc->>DB: Update PendingRemovalSuggestion status (e.g., 'applied_to_spotify' or 'failed_to_apply')
        end
    end

    %% Finalize Apply Changes Task
    Note over ApplySvc: Gathers all results (counts of successes/failures, specific error details)
    ApplySvc->>DB: Update apply_changes_job_status (finalStatus, completed_at, summary)
    deactivate ApplySvc

    %% Frontend Displays Final Result (from last poll)
    Note over FE: Last poll reflects final status and summary
    FE-->>User: UI: Display Final Summary Modal (success/failure, counts, errors)
```