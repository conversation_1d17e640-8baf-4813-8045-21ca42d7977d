# Epic 1: Project Foundation & Core Spotify Integration

**Goal:** To establish the foundational project structure (Monorepo with Python backend and web frontend placeholders), API scaffolding (e.g., FastAPI), initial database setup (e.g., PostgreSQL with pgvector), and implement core Spotify authentication (including requesting necessary scopes for playlist reading and modification) and basic playlist data retrieval capabilities [user_input_file_1 source: 954]. This Epic will provide the essential groundwork for all subsequent functionalities [user_input_file_1 source: 955].

## Story 1.1: Initial Project & Monorepo Setup
**User Story:** As a Developer (representing the system's need for a proper foundation), I want the initial project Monorepo to be set up with distinct placeholders for the Python backend and web frontend, so that subsequent development for both can proceed in an organized manner within a single repository [user_input_file_1 source: 956].
**Acceptance Criteria (ACs):**
1. A Git repository `shall` be initialized for the project [user_input_file_1 source: 957].
2. A root directory structure for a Monorepo `shall` be created, clearly delineating areas for backend code (e.g., backend/), frontend code (e.g., frontend/), shared documentation (e.g., docs/), and utility scripts (e.g., scripts/) [user_input_file_1 source: 958].
3. Basic project configuration files `shall` be present at the root level (e.g., a comprehensive .gitignore file suitable for Python and Node.js/web development, a root README.md file with a project title and brief description) [user_input_file_1 source: 959].
4. Minimal placeholder application stubs (e.g., a "hello world" script or basic runnable application) `shall` exist within both the backend/ (Python) and frontend/ (e.g., a simple HTML file or basic Node.js/Vite/Next.js app stub) directories to confirm their independent setup [user_input_file_1 source: 960].
5. Basic dependency management files `shall` be initialized for both the backend (Python, e.g., using uv with a pyproject.toml or requirements.txt structure compatible with uv) and the frontend (e.g., package.json for npm/yarn/pnpm) [user_input_file_1 source: 961].

## Story 1.2: Backend API Scaffolding (FastAPI)
**User Story:** As a Developer, I want a basic FastAPI application scaffolded for the backend, including initial configuration for environment variables, so that API endpoints can be progressively added and the backend can serve as the central logic hub [user_input_file_1 source: 962].
**Acceptance Criteria (ACs):**
1. The FastAPI framework and its core dependencies (like Uvicorn for serving) `shall` be added as dependencies to the Python backend project (e.g., in the pyproject.toml managed by uv) [user_input_file_1 source: 963].
2. A minimal FastAPI application `shall` be created within the backend/ directory structure [user_input_file_1 source: 964].
3. This application `shall` include at least one basic, unauthenticated health check endpoint (e.g., /health) that returns a simple success response (e.g., {"status": "ok"}) [user_input_file_1 source: 965].
4. The backend FastAPI application `shall` be runnable locally using a development server (e.g., Uvicorn command like uvicorn main:app --reload) [user_input_file_1 source: 966].
5. A basic structure for organizing the FastAPI application `shall` be established (e.g., a main application file main.py, and potentially an initial routers subdirectory for future endpoint organization) [user_input_file_1 source: 967].
6. A mechanism for managing environment variables and application configuration (e.g., using Pydantic settings to load from .env files or system environment variables) `shall` be implemented and demonstrated for basic settings like application title or API version [user_input_file_1 source: 968].
7. A basic, structured logging mechanism (e.g., using Python's standard logging module configured for a clear, consistent output format like JSON, or a simple text format with timestamps and levels) `shall` be integrated into the FastAPI application, allowing for configurable log levels (e.g., DEBUG, INFO, ERROR) and basic request/application event logging [user_input_file_1 source: 969].
8. A basic framework for centralized API exception handling `shall` be established within the FastAPI application (e.g., by implementing a few common custom exception handlers) to promote consistent JSON error responses for typical HTTP errors (such as 400, 401, 403, 404, 500) [user_input_file_1 source: 970].

## Story 1.3: Initial Database Setup (PostgreSQL + pgvector)
**User Story:** As a System Administrator (representing the system's data persistence needs), I want an initial PostgreSQL database (or a connection to a managed service) to be set up, including the pgvector extension and basic backend connectivity, so that application data (like user tokens, playlist DNA, track info) can eventually be stored and queried [user_input_file_1 source: 971].
**Acceptance Criteria (ACs):**
1. A PostgreSQL database instance `shall` be accessible for the development environment [user_input_file_1 source: 972].
2. This can be a locally running instance (e.g., via Docker) or a connection to a cloud-hosted free-tier PostgreSQL service that is compatible with the pgvector extension (as identified in our research document) [user_input_file_1 source: 973].
3. The pgvector extension `shall` be successfully installed and enabled in the target PostgreSQL database, confirming its availability for vector operations [user_input_file_1 source: 974].
4. Basic database connection logic `shall` be implemented within the Python backend (FastAPI application) [user_input_file_1 source: 975].
5. This logic will allow the application to connect to the configured PostgreSQL database [user_input_file_1 source: 976].
6. An Object-Relational Mapper (ORM) (e.g., SQLAlchemy, aligning with FastAPI/Pydantic ecosystem) and an associated database migration tool (e.g., Alembic) `shall` be integrated into the backend project and configured to connect to the PostgreSQL database [user_input_file_1 source: 977].
7. An initial migration `shall` be created and successfully applied to establish at least one placeholder table (e.g., an app_settings or users table stub), thereby verifying the complete database interaction and schema management workflow [user_input_file_1 source: 978].
8. Database connection credentials (username, password, host, port, database name) `shall` be managed securely (e.g., through environment variables loaded by the backend's configuration system) and not hardcoded in the application [user_input_file_1 source: 979].

## Story 1.4: Core Spotify Authentication & Token Management
**User Story:** As a User (the primary system user), I want to securely authenticate the application with my Spotify account using OAuth 2.0 (requesting all necessary playlist read/write scopes), so that the application can access my playlist data and make changes on my behalf, with tokens being securely managed by the backend [user_input_file_1 source: 980].
**Acceptance Criteria (ACs):**
1. The backend API (FastAPI application established in Story 1.2) `shall` provide endpoints to initiate the Spotify OAuth 2.0 Authorization Code Flow (e.g., an endpoint that redirects the user to Spotify's authorization page and an endpoint for Spotify to redirect back to after user authorization) [user_input_file_1 source: 981].
2. During the authentication initiation, the application `shall` correctly request the necessary Spotify API scopes, including at least: playlist-read-private, playlist-read-collaborative, playlist-modify-public, and playlist-modify-private [user_input_file_1 source: 982].
3. Upon successful user authorization and callback from Spotify, the backend `shall` securely exchange the authorization code for Spotify access tokens (access token and refresh token) [user_input_file_1 source: 983].
4. The received Spotify access token and refresh token `shall` be securely stored by the backend system (e.g., in the PostgreSQL database set up in Story 1.3), associated with the application's user [user_input_file_1 source: 984].
5. The backend `shall` implement a mechanism to use the stored refresh token to obtain a new access token from Spotify when the current access token is expired or nearing expiration [user_input_file_1 source: 985].
6. The placeholder frontend application (from Story 1.1) `shall` include a basic UI element (e.g., a "Connect to Spotify" button or link) that, when activated, directs the user to the backend endpoint that initiates the Spotify authentication flow [user_input_file_1 source: 986].
7. Upon successful completion of the authentication flow, the user `shall` be redirected back to a designated page or state within the placeholder frontend application, and the backend `should` indicate a successful token acquisition (e.g., by logging or a simple status update accessible for debugging) [user_input_file_1 source: 987].

## Story 1.5: Basic Spotify Playlist Data Retrieval
**User Story:** As the System, I want to be able to retrieve a list of the authenticated user's playlists and fetch the detailed contents (tracks, snapshot_id) of a specific user-selected playlist using the Spotify API, so that this data can be used as input for further analysis [user_input_file_1 source: 988].
**Acceptance Criteria (ACs):**
1. The backend API (FastAPI application) `shall` provide a secure endpoint that, using the authenticated user's stored Spotify access token (from Story 1.4), fetches a list of the user's Spotify playlists [user_input_file_1 source: 989].
2. This list should include at least the playlist ID and name for each playlist [user_input_file_1 source: 990].
3. The backend API `shall` provide another secure endpoint that, given a Spotify playlist ID (obtained from AC1), retrieves all tracks from that playlist [user_input_file_1 source: 991].
4. For each track, the retrieved data `shall` include at least its Spotify ID, name, and primary artist(s) information [user_input_file_1 source: 992].
5. The endpoint for fetching specific playlist details (AC2) `shall` also retrieve and make available the playlist's current snapshot_id from Spotify [user_input_file_1 source: 993].
6. Basic error handling `shall` be implemented in the backend for these Spotify API interactions [user_input_file_1 source: 994].
7. This includes handling potential issues like invalid playlist IDs, token errors, or Spotify API service unavailability, returning appropriate HTTP status codes and error messages from the backend API [user_input_file_1 source: 995].
8. The functionality to list playlists and fetch specific playlist details `shall` be verifiable through API calls to the backend (e.g., using a tool like Postman or curl for developer testing), with responses confirming the expected data structure and content [user_input_file_1 source: 996].