# Frontend Styling Approach and Guide

This document outlines the core styling philosophy and approach for the Playlist Intelligence Agent MVP frontend, primarily based on Tailwind CSS. For broader branding and visual design specifications (like color palettes, typography scales defined by the UI/UX Spec), refer to the main UI/UX Specification document.

## Core Styling Solution: Tailwind CSS
**Tailwind CSS (Latest Stable)** will be our utility-first CSS framework for styling the Vue.js frontend efficiently.

* **Configuration File(s):** `tailwind.config.js`, `postcss.config.js`.
* **Key Conventions:**
    * Primarily use utility classes directly in templates for most styling needs.
    * For more complex, reusable styled elements or components with many utility classes, the preferred approach is to create dedicated Vue components that encapsulate these styles by composing Tailwind utilities directly in their templates.
    * If direct composition becomes unwieldy, using `@apply` within component style blocks (`<style scoped>`) for specific, well-defined custom component classes is acceptable, but should be used judiciously to maintain the benefits of the utility-first approach.
    * Custom theme extensions (colors, spacing, fonts, etc., to align with the UI/UX Specification's dark grey theme and other visual guidelines) will be defined in `tailwind.config.js` under the `theme.extend` section.
    * Global base styles or layer customizations for Tailwind will be handled in `src/assets/styles/main.css`.
* **Dark Theme:** The application will feature a dark grey theme as specified in the UI/UX Specification. Tailwind's dark mode variant (`dark:`) can be used if a light/dark toggle were ever introduced, but for MVP, styles will directly implement the dark theme.