# Epic 4: Removal Suggestion Engine

**Goal:** To enable the system to analyze the current playlist against its "Playlist DNA" to identify songs that are candidates for removal (due to vibe deviation or other criteria like release age), and to generate AI-powered justifications for these removal suggestions, while respecting protection periods for new songs [user_input_file_1 source: 1093].

## Story 4.1: Implement Vibe Outlier Detection for Playlist Tracks (Finalized with Revisions)
**User Story:** As the System, I want to analyze all tracks currently in the managed playlist against the stored "Playlist DNA" to identify and score songs that are potential "vibe outliers" based on their musical characteristics, so these can be considered for removal [user_input_file_1 source: 1094].
**Acceptance Criteria (ACs):**
1. The system `shall` retrieve the current list of all tracks (Spotify IDs) for the managed playlist [user_input_file_1 source: 1095].
2. For each track, its song vector (composed of numerical audio features and track popularity, consistent with Story 2.3 AC5) `shall` be either retrieved from a local cache/database if previously computed and stored, or `shall` be computed by fetching its audio features and popularity from Spotify using an efficient batching approach if these are not already available from the most recent 'Playlist DNA' computation process of Epic 2 [user_input_file_1 source: 1096].
3. The system `shall` retrieve the stored "Playlist DNA vector" (the centroid vector from Story 2.3) for the current playlist from the database [user_input_file_1 source: 1097].
4. For each track currently in the playlist, a numerical deviation score `shall` be calculated by comparing its song vector to the "Playlist DNA vector" [user_input_file_1 source: 1098].
5. The primary method for the MVP `shall` be Mahalanobis distance [user_input_file_1 source: 1099].
6. If Mahalanobis distance presents unforeseen implementation complexities prohibitive for the MVP, Cosine distance (using L2 normalized vectors) `shall` be used as a documented fallback [user_input_file_1 source: 1100].
7. A configurable method or threshold (e.g., tracks with deviation scores in the top X percent, or exceeding a specific calculated statistical distance) `shall` be used to flag tracks as potential "vibe outliers" [user_input_file_1 source: 1101].
8. For the MVP, this threshold will initially be a system-defined default, with considerations for future configurability [user_input_file_1 source: 1102].
9. Each track in the playlist that is processed `shall` have its calculated deviation score and its "potential vibe outlier" status (e.g., true/false) associated with it for subsequent filtering stages [user_input_file_1 source: 1103].
10. The outlier detection process `shall` gracefully handle playlists with very few tracks where statistical measures like a covariance matrix (required for Mahalanobis distance) might be unstable or ill-defined (e.g., by automatically falling back to Cosine distance or by skipping vibe outlier detection if the playlist size is below a configurable minimum, such as 15-20 tracks, and logging this action) [user_input_file_1 source: 1104].

## Story 4.2: Apply Configurable Filtering Rules to Removal Candidates (Finalized with Revisions)
**User Story:** As the System, I want to apply configurable filtering rules, such as song release age and protection for newly added songs, to the list of potential vibe outliers (or to all playlist tracks for other criteria), so that removal suggestions are contextually appropriate and fair [user_input_file_1 source: 1105].
**Acceptance Criteria (ACs):**
1. The system `shall` take as input the list of all tracks in the managed playlist, each with its associated "potential vibe outlier" status and deviation score (from Story 4.1), along with each track's release_date [user_input_file_1 source: 1106].
2. Release Age Filtering (Optional): If a user-defined preference for a maximum song release_age is active and configured by the user (as per Functional Requirement 6.1b, e.g., "songs older than 10 years from current date"), the system `shall` identify tracks whose release_date makes them older than this preference [user_input_file_1 source: 1107].
3. These tracks `shall` be flagged as candidates for removal specifically due to "release age" [user_input_file_1 source: 1108].
4. 30-Day New Song Protection (FR 6.3): For any track flagged as a potential removal candidate (either due to 'vibe outlier' status from Story 4.1 or 'release age' from AC2 above), the system `shall` apply a protection rule:
    a. A configurable 'new song protection period' (e.g., defaulting to 30 days, as per FR 6.3) `shall` be used [user_input_file_1 source: 1109].
    b. For the MVP, a song `shall` be primarily considered 'new' for protection purposes if its release_date (obtained from Spotify) falls within this protection period relative to the current date [user_input_file_1 source: 1110]. (A future enhancement might involve the system tracking its own 'date_added_to_playlist' for songs it adds, to allow more precise protection for those specific additions) [user_input_file_1 source: 1111].
    c. 'New' songs (as defined in 3b) that were flagged primarily for 'vibe deviation' `shall` be excluded from the current list of removal suggestions [user_input_file_1 source: 1112].
    d. Tracks flagged for exceeding a 'release age' filter (AC2) may still be suggested for removal even if their release_date is recent (e.g., a new re-release of a very old song if the filter is strict about original era), depending on the configured interaction between these rules [user_input_file_1 source: 1113].
5. The system `shall` output a refined list of songs that are final candidates for removal suggestion, clearly noting the primary reason (e.g., "vibe_outlier," "release_age_exceeded") for each [user_input_file_1 source: 1114].
6. Songs protected under the 30-day rule (for vibe outliers) `shall` be excluded from this final list [user_input_file_1 source: 1115].
7. The application of all filtering rules, any exclusions due to protection periods, and the reasons for flagging each final candidate `shall` be logged for transparency and debugging [user_input_file_1 source: 1116].

## Story 4.3: Generate LLM Justifications for Removal Suggestions (Finalized with Revisions)
**User Story:** As the System, I want to take songs that have been flagged as strong candidates for removal (after filtering in Story 4.2) and use an LLM to generate a concise and relevant justification for each one, explaining specifically how it deviates from the playlist's core DNA (if a vibe outlier) or why it meets other removal criteria (like its release age) [user_input_file_1 source: 1117].
**Acceptance Criteria (ACs):**
1. The system `shall` take as input the refined list of candidate songs for removal (from Story 4.2), along with the specific reason each was flagged (e.g., "vibe_outlier" with its deviation score/details, or "release_age_exceeded" with the song's release year) [user_input_file_1 source: 1118].
2. For each removal candidate, the system `shall` programmatically prepare a structured prompt for the configured LLM API [user_input_file_1 source: 1119].
3. The prompt `shall` include:
    * Key characteristics of the 'Playlist DNA' (e.g., top 3-5 genres, top 3-5 artists, representative average values or target ranges for 2-3 pivotal audio features like valence/energy based on DNA's mean/standard deviation) [user_input_file_1 source: 1120].
    * Key characteristics of the candidate song being suggested for removal (its genre, artist, relevant audio features) [user_input_file_1 source: 1121].
    * The specific reason it was flagged. If the reason is 'vibe_outlier,' the prompt must also include specific details of the deviation, such as highlighting 1-2 key audio features of the song that most significantly differ from the Playlist DNA's characteristics and by how much (e.g., 'Song's energy is 0.9, significantly above the playlist's average energy of 0.5; song's instrumentalness is 0.8, while the playlist average is 0.1') [user_input_file_1 source: 1122].
    * If the reason is 'release_age_exceeded,' it should state the song's release year in relation to the preference [user_input_file_1 source: 1123].
4. The prompt `shall` explicitly instruct the LLM to generate a concise (e.g., maximum 25 words, this limit being configurable) and musically relevant justification explaining why the song is being suggested for removal, based on the provided context [user_input_file_1 source: 1124].
5. The system `shall` make an API call to the configured LLM service with the prepared prompt and retrieve the generated textual justification for each removal candidate [user_input_file_1 source: 1125].
6. The generated justification `shall` be stored in association with the respective removal candidate song [user_input_file_1 source: 1126].
7. The system `shall` implement basic error handling for LLM API calls [user_input_file_1 source: 1127].
8. If a justification cannot be successfully generated for a candidate song, a non-empty default placeholder justification (e.g., "This song is suggested for removal based on playlist analysis and filtering rules.") `shall` be used, and the specific LLM failure `shall` be logged [user_input_file_1 source: 1128].

## Story 4.4: Store Pending Removal Suggestions (Finalized)
**User Story:** As the System, I want to store all songs flagged for removal that have an AI-generated justification as pending removal suggestions in the database, so that they can be presented to the user for review [user_input_file_1 source: 1129].
**Acceptance Criteria (ACs):**
1. For every song identified as a removal suggestion (i.e., passed filters in Story 4.2 and has a justification from Story 4.3), the system `shall` prepare a new record for database storage [user_input_file_1 source: 1130].
2. This "pending removal suggestion" record `shall` include at least: the Spotify Track ID, track name, primary artist(s) names, the primary reason it was flagged for removal (e.g., "vibe_outlier," "release_age_exceeded"), its deviation score (if applicable for vibe outliers), the LLM-generated justification, a default status (e.g., "pending_review"), and the date/timestamp when it was identified as a removal suggestion [user_input_file_1 source: 1131].
3. These pending removal suggestion records `shall` be durably stored in a dedicated table (e.g., pending_removal_suggestions) in the PostgreSQL database [user_input_file_1 source: 1132].
4. Each record `shall` be linked to the managed playlist ID [user_input_file_1 source: 1133].
5. The database schema for the pending_removal_suggestions table `shall` be defined and implemented via a schema migration (using the tool established in Story 1.3, e.g., Alembic), including appropriate data types and any necessary indexes [user_input_file_1 source: 1134].
6. The system `shall` ensure that a new 'pending_review' removal suggestion for a specific Spotify Track ID is not created if an identical, active 'pending_review' removal suggestion for that same track ID already exists for the managed playlist in the database (to prevent cluttering the review queue with duplicates across syncs if a user hasn't actioned it) [user_input_file_1 source: 1135].
7. The operation to store pending removal suggestions `shall` handle potential database errors (e.g., connection issues, constraint violations) gracefully and `shall` log the successful storage of new suggestions or any failures encountered [user_input_file_1 source: 1136].