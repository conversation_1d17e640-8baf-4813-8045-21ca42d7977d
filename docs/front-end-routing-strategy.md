# Frontend Routing Strategy

Navigation and routing within the Vue.js SPA will be handled by **Vue Router (Latest Stable)**, as specified in the Main Architecture Document. Configuration will reside in `src/router/index.ts`.

## Route Definitions

Based on the UI/UX Specification (Section 3.1 Site Map/Core Views) and PRD Epic 5 stories:

| Path Pattern           | View Component (`src/views/...` or `src/features/.../views/...`) | Protection                      | Notes                                                                 |
| :--------------------- | :-------------------------------------------------- | :------------------------------ | :-------------------------------------------------------------------- |
| `/login-callback`      | `src/features/auth/views/AuthCallbackView.vue`      | `Public`                        | Handles Spotify callback redirect from backend; may not render UI itself, just processes and navigates. |
| `/select-playlist`     | `src/features/playlist/views/PlaylistSelectionView.vue` | `Authenticated`                 | Shown if no playlist is configured (Story 5.2).        |
| `/`                    | `src/views/DashboardView.vue`                       | `Authenticated`                 | Alias for `/dashboard`. Redirects to `/select-playlist` if not configured. |
| `/dashboard`           | `src/views/DashboardView.vue`                       | `Authenticated`                 | Main Overview/Dashboard screen (Story 5.3).                  |
| `/queue/discovery`     | `src/features/reviewQueue/views/DiscoveryQueueView.vue` | `Authenticated`                 | Discovery Queue (Story 5.4).                              |
| `/queue/removal`       | `src/features/reviewQueue/views/RemovalQueueView.vue`   | `Authenticated`                 | Removal Review Queue (Story 5.5).                           |
| `/settings`            | `src/views/SettingsView.vue`                        | `Authenticated`                 | Configuration/Settings screen (Story 5.7).                 |
| `/debug`               | `src/views/DebugView.vue`                           | `Authenticated`                 | Debugging screen (Story 5.8).                              |
| `/:pathMatch(.*)*`     | `src/views/NotFoundView.vue`                        | `Public`                        | 404 Page Not Found (Story 5.1).                           |

## Route Guards / Protection

* **Authentication Guard:** Implemented using Vue Router's `beforeEach` navigation guard in `src/router/index.ts`.
    * **Logic:**
        1.  Initialize `AuthStore` (e.g., `const authStore = useAuthStore();`). Before accessing store properties like `isAuthenticated` or `activeManagedPlaylistId`, ensure `authStore.checkAuthStatus()` has been called and awaited if its `status` is `'idle'` or if the data seems stale (e.g., on initial app load). This ensures the store reflects the latest backend session status.
        2.  If a route's `meta.requiresAuth` is true:
            * If `authStore.isAuthenticated` is false after the status check, redirect the user to the backend login initiation URL (e.g., `window.location.href = '/api/v1/auth/login'`).
            * If `authStore.isAuthenticated` is true but `authStore.activeManagedPlaylistId` is null (and the route is not `/select-playlist` or `/login-callback`), redirect to `/select-playlist`.
        3.  If a route's `meta.guestOnly` is true (e.g. a potential separate login page, not used in current flow):
            * If `authStore.isAuthenticated` is true, redirect to `/dashboard`.
    * Routes will have `meta: { requiresAuth: true }` as needed.
* **Authorization Guard:** Not applicable for MVP as it's a single-user application focused on their own data.