# Epic 3: New Release Discovery & Recommendation Engine

**Goal:** To implement the functionality for discovering new music releases (filtered for Canadian availability and relevance to the "Playlist DNA"), scoring their similarity to the playlist, and generating AI-powered justifications for recommending them as potential additions [user_input_file_1 source: 1038].

## Story 3.1: Implement New Release Search Strategy via Spotify API (Finalized)
**User Story:** As the System, I want to implement a strategy to search Spotify for new music releases (albums and singles) based on key artists and genres derived from the "Playlist DNA," utilizing appropriate API endpoints and filters (like market=CA and tag:new or year), so that a raw pool of potentially relevant new tracks can be identified for further processing [user_input_file_1 source: 1039].
**Acceptance Criteria (ACs):**
1. The system `shall` retrieve the top N artists (by frequency) and top M genres (by frequency) from the stored 'Playlist DNA' profile (from Epic 2) to use as seeds for the discovery process, where N and M are configurable system parameters (e.g., defaulting to 5 artists and 3 genres respectively) [user_input_file_1 source: 1040].
2. The system `shall` construct and execute search queries against the Spotify API's /search endpoint [user_input_file_1 source: 1041].
3. These queries `shall` effectively combine seed artists and/or genres with filters such as tag:new (for releases in the past two weeks) and/or year:current_year (or a relevant recent year range like year:2024-2025 as supported by API documentation), and the market=CA parameter to target Canadian relevance [user_input_file_1 source: 1042].
4. The search queries made to the /search endpoint `shall` specify type=album,single to ensure both albums and standalone singles are considered in the discovery results [user_input_file_1 source: 1043].
5. The system `shall` implement logic to iterate through multiple seed artists and genres (from AC1) to broaden the search scope, and this iteration logic `shall` gracefully handle cases where the 'Playlist DNA' contains fewer than the configured N artists or M genres (e.g., use all available seeds without error) [user_input_file_1 source: 1044].
6. As a supplementary discovery method, the system `shall` also implement calls to the GET /artists/{id}/albums endpoint for the top N artists derived from the "Playlist DNA" [user_input_file_1 source: 1045].
7. These calls `shall` filter for album and single in the include_groups parameter and specify market=CA [user_input_file_1 source: 1046].
8. All discovered items (albums, and tracks from "single" type albums) `shall` be collected, and for each item, at least its Spotify ID, name, primary artist(s) (names and IDs), and its release_date and release_date_precision `shall` be extracted and stored temporarily [user_input_file_1 source: 1047].
9. The system `shall` correctly implement pagination handling for all relevant Spotify API calls (e.g., /search, /artists/{id}/albums) to ensure a comprehensive list of potential new releases is retrieved, not just the first page of results [user_input_file_1 source: 1048].
10. Robust error handling and detailed logging `shall` be implemented for all Spotify API interactions within this story, capturing any issues encountered during the search and discovery process [user_input_file_1 source: 1049].

## Story 3.2: Filter New Releases by Date & Canadian Availability Verification (Finalized)
**User Story:** As the System, I want to filter the raw list of discovered new releases to include only those actually released since the last successful playlist sync and definitively available for streaming in Canada, so that only truly new and accessible tracks are considered for recommendation [user_input_file_1 source: 1050].
**Acceptance Criteria (ACs):**
1. The system `shall` take as input the raw list of potential new releases (including track/album IDs, names, artists, and release dates) collected in Story 3.1 [user_input_file_1 source: 1051].
2. The system `shall` retrieve the timestamp of the last successful full synchronization process for the managed playlist [user_input_file_1 source: 1052].
3. A mechanism to record this timestamp upon successful completion of each full sync (e.g., in a dedicated sync log table or alongside the playlist's general metadata in the database) `shall` be implemented as part of this story if not already present [user_input_file_1 source: 1053].
4. If no previous sync timestamp exists (e.g., first run), a configurable default lookback period (e.g., past 30 days) `shall` be used [user_input_file_1 source: 1054].
5. For each item in the raw list from Story 3.1, its release_date `shall` be accurately parsed (taking into account release_date_precision from Spotify, e.g., year, month, or day) and compared against the last sync timestamp (or default lookback period) [user_input_file_1 source: 1055].
6. Only items confirmed to be released after this timestamp `shall` be retained for further processing [user_input_file_1 source: 1056].
7. The date comparison logic `shall` clearly define how Spotify's release_date_precision (year, month, day) is handled when determining 'newness' relative to the last sync timestamp (e.g., a release with 'year' precision for the current year is considered new until the year ends; a 'month' precision is considered new until that month ends) [user_input_file_1 source: 1057].
8. For each remaining track (or for all tracks within remaining albums), its definitive playability in Canada (CA) `shall` be verified by making a call to the Spotify API's GET /tracks/{track_id} endpoint with the market=CA parameter, and confirming is_playable: true in the response as the primary indicator, while also ensuring 'CA' is present in the available_markets array [user_input_file_1 source: 1058].
9. If an item from Story 3.1 was an album, its constituent tracks `shall` be individually extracted (their Spotify IDs obtained) and each track `shall` be subjected to the Canadian availability verification outlined in AC4 [user_input_file_1 source: 1059].
10. Only unique tracks (identified by their unique Spotify Track ID) that are confirmed as both "new" (released since the last sync/within lookback period) and "available and playable in Canada" `shall` be included in the filtered list passed to the next stage [user_input_file_1 source: 1060].
11. Any duplicate tracks encountered during processing `shall` be resolved to a single entry [user_input_file_1 source: 1061].
12. The filtering process `shall` log key metrics, such as the number of items received, the number of items dropped due to release date filtering, and the number of items dropped due to Canadian availability filtering, to provide insight into the filtering effectiveness [user_input_file_1 source: 1062].

## Story 3.3: Score New Releases for Similarity to Playlist DNA (Finalized)
**User Story:** As the System, I want to fetch audio features for the filtered new releases and then score each track based on its musical similarity to the stored "Playlist DNA" (using the DNA vector and chosen similarity metric like Mahalanobis distance or cosine similarity), so that we can quantify how well each new song matches the playlist's established vibe [user_input_file_1 source: 1063].
**Acceptance Criteria (ACs):**
1. The system `shall` take as input the filtered list of new, Canadian-available tracks (from Story 3.2) [user_input_file_1 source: 1064].
2. For each track in this input list, the system `shall` fetch its audio features using the Spotify API's /audio-features endpoint (batched efficiently) [user_input_file_1 source: 1065].
3. A song vector `shall` be constructed for each new track using the identical set of numerical audio features and track popularity that were used to define the "Playlist DNA vector" (as specified in Story 2.3, AC2 & AC5) [user_input_file_1 source: 1066].
4. The system `shall` retrieve the stored "Playlist DNA vector" for the currently managed playlist from the database (as stored in Story 2.3) [user_input_file_1 source: 1067].
5. A numerical similarity score `shall` be calculated between each new song's vector and the "Playlist DNA vector" [user_input_file_1 source: 1068].
6. The primary method for the MVP `shall` be Mahalanobis distance [user_input_file_1 source: 1069].
7. If significant, unforeseen implementation complexities with Mahalanobis distance arise that are prohibitive for the MVP timeframe, Cosine similarity `shall` be used as a documented fallback method, with this decision logged [user_input_file_1 source: 1070].
8. Each processed new song `shall` have its calculated similarity score stored or associated with it for further processing [user_input_file_1 source: 1071].
9. The system `shall` gracefully handle cases where audio features for a new track cannot be retrieved from Spotify (e.g., by logging the issue, assigning a default non-match score, or excluding the track from similarity scoring and further recommendation) [user_input_file_1 source: 1072].
10. If Cosine similarity is employed (as per AC5 fallback), the song vectors and the Playlist DNA vector `shall` be L2 normalized before the similarity calculation to ensure fair comparison [user_input_file_1 source: 1073]. (Mahalanobis distance inherently handles feature covariance and scaling considerations).

## Story 3.4: Generate LLM Justifications for Relevant Recommendations (Finalized)
**User Story:** As the System, I want to take new songs that meet a defined similarity threshold, prepare a rich contextual prompt including "Playlist DNA" highlights and song features, and then use an LLM to generate a concise and relevant justification explaining why each song is a good match for the playlist [user_input_file_1 source: 1074].
**Acceptance Criteria (ACs):**
1. The system `shall` take as input the list of new songs along with their associated similarity scores (from Story 3.3) [user_input_file_1 source: 1075].
2. A similarity threshold (initially a system-defined default value, with future user-configurability considered post-MVP) `shall` be used to identify "relevant" songs that are strong candidates for recommendation [user_input_file_1 source: 1076].
3. For each candidate song that meets or exceeds this similarity threshold, the system `shall` programmatically prepare a structured prompt for the configured LLM API (e.g., Google Gemini 1.5 Flash, as per research findings) [user_input_file_1 source: 1077].
4. The prompt sent to the LLM `shall` include:
    * Key characteristics of the "Playlist DNA" (e.g., top 3-5 genres, top 3-5 artists, representative average values or ranges for 2-3 pivotal audio features like valence, energy, tempo) [user_input_file_1 source: 1078].
    * Key characteristics of the candidate song (e.g., its specific genre(s), artist(s), and its values for the same pivotal audio features highlighted from the DNA) [user_input_file_1 source: 1079].
5. The prompt `shall` explicitly instruct the LLM to generate a concise (e.g., maximum 25 words, this limit being configurable) and musically relevant justification explaining why the candidate song is considered a good match for the playlist, based on the provided contextual data [user_input_file_1 source: 1080].
6. The system `shall` make an API call to the configured LLM service with the prepared prompt and retrieve the generated textual justification for each candidate song [user_input_file_1 source: 1081].
7. The generated justification `shall` be stored in association with the respective candidate song [user_input_file_1 source: 1082].
8. The system `shall` implement basic error handling for LLM API calls (e.g., timeouts, API errors, content filtering issues) [user_input_file_1 source: 1083].
9. If a justification cannot be successfully generated for a candidate song, a non-empty default placeholder justification (e.g., 'This track shares key musical characteristics with your playlist.') `shall` be used, and the specific LLM failure `shall` be logged for troubleshooting [user_input_file_1 source: 1084].

## Story 3.5: Store Pending Recommendations (Finalized)
**User Story:** As the System, I want to store all new songs that meet the similarity threshold and have an AI-generated justification as pending recommendations in the database, so that they can be presented to the user for review [user_input_file_1 source: 1085].
**Acceptance Criteria (ACs):**
1. For every song identified as a relevant recommendation candidate (i.e., meets the similarity threshold as per Story 3.4 AC2 and has a justification, even if a fallback one, as per Story 3.4 AC8), the system `shall` prepare a new record for database storage [user_input_file_1 source: 1086].
2. This "pending recommendation" record `shall` include at least: the Spotify Track ID, track name, primary artist(s) names, album release date, the calculated similarity score (from Story 3.3), the LLM-generated justification (from Story 3.4), a default status (e.g., "pending_review"), and the date/timestamp when it was discovered and processed as a recommendation [user_input_file_1 source: 1087].
3. These pending recommendation records `shall` be durably stored in a dedicated table (e.g., pending_recommendations) in the PostgreSQL database [user_input_file_1 source: 1088].
4. Each record `shall` be linked to the managed playlist ID [user_input_file_1 source: 1089].
5. The database schema for the pending_recommendations table `shall` be defined and implemented via a schema migration (using the tool established in Story 1.3, e.g., Alembic), including appropriate data types and any necessary indexes (e.g., on playlist ID and status) [user_input_file_1 source: 1090].
6. The system `shall` ensure that a new 'pending_review' recommendation for a specific Spotify Track ID is not created if an identical, active 'pending_review' recommendation for that same track ID already exists for the managed playlist in the database [user_input_file_1 source: 1091].
7. The operation to store pending recommendations `shall` handle potential database errors (e.g., connection issues, constraint violations) gracefully and `shall` log the successful storage of new recommendations or any failures encountered [user_input_file_1 source: 1092].