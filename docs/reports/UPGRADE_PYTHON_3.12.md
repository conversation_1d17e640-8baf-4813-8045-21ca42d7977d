# Python 3.12 and Dependencies Upgrade Guide

This document outlines the changes made to upgrade the Playlist Intelligence Agent project from Python 3.11.9 to Python 3.12.8 and update all dependencies to their latest stable versions.

## Summary of Changes

### Python Version Update
- **From**: Python 3.11.9
- **To**: Python 3.12.8 (latest stable)
- **Rationale**: Python 3.12 offers improved performance, better error messages, and new language features

### Dependency Updates

| Package | Previous Version | New Version | Breaking Changes |
|---------|------------------|-------------|------------------|
| fastapi | 0.110.1 | 0.115.12 | Lifespan events migration |
| uvicorn | 0.29.0 | 0.32.1 | None |
| pydantic | 2.7.1 | 2.10.4 | None (already v2) |
| pydantic-settings | 2.2.1 | 2.7.1 | None |
| sqlalchemy | 2.0.29 | 2.0.36 | None (already v2) |
| alembic | 1.13.1 | 1.14.0 | None |
| asyncpg | 0.29.0 | 0.30.0 | None |
| psycopg2-binary | 2.9.9 | 2.9.10 | None |
| spotipy | 2.23.0 | 2.24.0 | None |
| cryptography | 42.0.7 | 44.0.0 | None |

## Build System Configuration Fix

**File**: `backend/pyproject.toml`

**Issue**: Hatchling build backend couldn't locate the package files because the project structure uses `src/app` instead of a directory matching the package name.

**Solution**: Added explicit package configuration:
```toml
[tool.hatch.build.targets.wheel]
packages = ["src/app"]
```

This tells Hatchling to include the `src/app` directory in the wheel package.

## Code Changes Made

### 1. FastAPI Lifespan Events Migration

**File**: `backend/src/app/main.py`

**Before** (deprecated):
```python
@app.on_event("startup")
async def startup_event():
    logger.info("Application startup commencing.")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Application shutdown initiated.")
```

**After** (modern approach):
```python
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Application startup commencing.")
    yield
    # Shutdown
    logger.info("Application shutdown initiated.")

app = FastAPI(
    title=settings.APP_TITLE,
    version=settings.API_VERSION,
    description="API for managing Spotify playlists with AI-driven insights.",
    lifespan=lifespan,
)
```

### 2. Pydantic v2 Configuration Updates

**File**: `backend/src/app/config.py`

**Changes**:
- Replaced `@validator` with `@field_validator` and `@computed_field`
- Updated database URL construction using computed fields
- Simplified field validation logic

**Before**:
```python
from pydantic import PostgresDsn, validator

@validator("DATABASE_URL_ASYNC", pre=False, always=True)
def assemble_db_connection_async(cls, v: Optional[str], values: dict) -> any:
    if isinstance(v, str): return v
    return PostgresDsn.build(...)
```

**After**:
```python
from pydantic import field_validator, computed_field

@computed_field
@property
def DATABASE_URL_ASYNC(self) -> str:
    return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

@field_validator("SPOTIFY_CLIENT_ID", "SPOTIFY_CLIENT_SECRET", ...)
@classmethod
def check_required_settings(cls, v: Optional[str]) -> str:
    if not v:
        raise ValueError("This environment variable must be set.")
    return v
```

### 3. SQLAlchemy 2.0 Session Management

**File**: `backend/src/app/db/session.py`

**Changes**:
- Updated to use `async_sessionmaker` instead of `sessionmaker`
- Replaced `declarative_base()` with `DeclarativeBase` class
- Improved type hints for async generators

**Before**:
```python
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

AsyncSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine, class_=AsyncSession, expire_on_commit=False)
Base = declarative_base()
```

**After**:
```python
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.orm import DeclarativeBase

AsyncSessionLocal = async_sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False
)

class Base(DeclarativeBase):
    pass
```

## Updated Documentation

### Files Updated:
1. `backend/pyproject.toml` - Updated Python version requirement and all dependencies
2. `docs/tech-stack.md` - Updated version numbers in the tech stack table
3. `backend/README.md` - Added Python 3.12 prerequisite and updated setup instructions

### Setup Instructions Changes:
- Added Python 3.12.0+ requirement
- Updated virtual environment creation: `uv venv --python 3.12`
- Updated dependency installation: `uv pip install -e .`

## Compatibility Notes

### What Still Works:
- All existing API endpoints and functionality
- Database migrations (Alembic)
- Spotify authentication flow
- Docker Compose setup
- Frontend integration

### What's Improved:
- Better performance with Python 3.12
- More robust error handling in FastAPI
- Improved type safety with latest Pydantic
- Better async session management with SQLAlchemy

## Testing Recommendations

After upgrading, test the following:

1. **Application Startup**: Verify the app starts without errors
2. **Database Connectivity**: Test health check endpoints
3. **Spotify Authentication**: Test the OAuth flow
4. **API Endpoints**: Test playlist retrieval endpoints
5. **Database Migrations**: Run `alembic upgrade head`

## Migration Steps for Developers

1. **Update Python**: Install Python 3.12.8 or later
2. **Recreate Virtual Environment**:
   ```bash
   cd backend
   rm -rf .venv
   uv venv --python 3.12
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```
3. **Install Dependencies**:
   ```bash
   uv pip install -e .
   ```

   **Note**: The installation now works correctly with the updated `pyproject.toml` configuration that specifies the package location.

4. **Verify Installation**:
   ```bash
   uv pip list | grep -E "(fastapi|pydantic|sqlalchemy)"
   ```

   Expected output should show the updated versions:
   - fastapi 0.115.12
   - pydantic 2.10.4
   - sqlalchemy 2.0.36

5. **Test Application** (requires environment setup):
   ```bash
   # Copy and configure environment file
   cp .env.example .env
   # Edit .env with your Spotify credentials and encryption keys

   # Run the application
   cd src
   uvicorn app.main:app --reload
   ```

## Rollback Plan

If issues arise, you can rollback by:
1. Reverting the changes in `backend/pyproject.toml`
2. Reverting the code changes in the affected files
3. Recreating the virtual environment with Python 3.11.9

The database schema and migrations remain unchanged, so no database rollback is needed.
