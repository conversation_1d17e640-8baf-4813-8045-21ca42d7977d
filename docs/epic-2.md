# Epic 2: "Playlist DNA" Engine & Profile Generation

**Goal:** To enable the system to connect to a user-selected Spotify playlist, thoroughly analyze its tracks (extracting audio features, artist details, genres), compute a comprehensive "Playlist DNA" profile representing its core musical characteristics, and store this profile [user_input_file_1 source: 997].

## Story 2.1: Fetch Full Track Details (including Audio Features)
**User Story:** As the System, I want to efficiently retrieve detailed information, including all relevant audio features (e.g., danceability, energy, valence, tempo, instrumentalness) and key metadata (e.g., popularity), for all tracks in a given Spotify playlist, so that this comprehensive data is available for "Playlist DNA" computation [user_input_file_1 source: 998].
**Acceptance Criteria (ACs):**
1. The backend system `shall` define an internal function or service method that accepts a Spotify Playlist ID (obtained via Story 1.5 functionality) and utilizes the authenticated user's Spotify access token [user_input_file_1 source: 999].
2. This function/service `shall` use the Spotify API to retrieve all track items from the specified playlist, correctly handling pagination if the playlist contains more tracks than the API limit per single call (e.g., typically 50 or 100 items for playlist tracks) [user_input_file_1 source: 1000].
3. For each track item retrieved from the playlist, the system `shall` extract and temporarily store at least its Spotify Track ID, name, primary artist(s) (names and Spotify IDs), album name, and album release date [user_input_file_1 source: 1001].
4. Using the collected Spotify Track IDs, the system `shall` make efficient, batched calls to the Spotify API's /audio-features endpoint to retrieve the audio features for all tracks in the playlist, respecting the API's batch size limits (e.g., up to 100 track IDs per call) [user_input_file_1 source: 1002].
5. The retrieved audio features for each track `shall` include at least: danceability, energy, key, loudness, mode, speechiness, acousticness, instrumentalness, liveness, valence, tempo, and duration_ms [user_input_file_1 source: 1003].
6. The system `shall` also retrieve or ensure it has access to the popularity score (0-100) for each track in the playlist [user_input_file_1 source: 1004].
7. The comprehensive collected data (including track metadata like ID, name, artists, release date, popularity, and all specified audio features) for all tracks in the playlist `shall` be structured (e.g., into a list of Python objects or a pandas DataFrame within the backend) and made readily available for the next stage of "Playlist DNA" computation [user_input_file_1 source: 1005].
8. Basic error handling `shall` be implemented for all Spotify API calls within this story [user_input_file_1 source: 1006].
9. This includes logging any errors encountered and gracefully handling scenarios where specific track details or audio features might be missing or partially unavailable (e.g., by noting missing data rather than failing the entire process) [user_input_file_1 source: 1007].

## Story 2.2: Extract Artist & Genre Profiles for Playlist
**User Story:** As the System, I want to compile a comprehensive profile of all artists and their associated genres present in a given Spotify playlist (by fetching artist details from Spotify), so that this rich contextual information can contribute significantly to the "Playlist DNA" [user_input_file_1 source: 1008].
**Acceptance Criteria (ACs):**
1. The system `shall` take as input the list of tracks, including their associated artist IDs, previously retrieved for a specific Spotify playlist (as per Story 2.1) [user_input_file_1 source: 1009].
2. From this input, the system `shall` compile a list of all unique artist Spotify IDs present across all tracks in the playlist [user_input_file_1 source: 1010].
3. For each unique artist ID (or batches of IDs), the system `shall` use the Spotify API (e.g., the GET /artists endpoint) to fetch detailed artist information [user_input_file_1 source: 1011].
4. The primary information to retrieve from each artist's details is their list of associated genres [user_input_file_1 source: 1012].
5. API calls to fetch artist details `shall` be implemented efficiently, utilizing batching if the Spotify API endpoint supports it (e.g., the /artists endpoint allows for multiple artist IDs per call) to minimize the number of requests [user_input_file_1 source: 1013].
6. The system `shall` create an internal data structure (e.g., a dictionary or map) that associates each unique artist ID from the playlist with the list of genres returned for that artist by the Spotify API [user_input_file_1 source: 1014].
7. The system `shall` then aggregate all unique genres found across all artists in the playlist [user_input_file_1 source: 1015].
8. This aggregated genre profile for the playlist (which might include genres and their frequency or simply a unique set) `shall` be compiled [user_input_file_1 source: 1016].
9. The system `shall` gracefully handle scenarios where an artist might have no genres listed in Spotify or if artist details cannot be fetched for a particular ID (e.g., by logging the situation and continuing with the available data, ensuring the process doesn't fail) [user_input_file_1 source: 1017].
10. The compiled artist-to-genres mapping and the aggregated playlist genre profile `shall` be structured and made readily available for the subsequent "Playlist DNA" computation stage (Story 2.3) [user_input_file_1 source: 1018].

## Story 2.3: Compute & Store "Playlist DNA" Profile (Revised)
**User Story:** As the System, I want to compute a "Playlist DNA" profile by aggregating the collected track audio features, artist information, and genre data for a given playlist, and then securely store this profile (including the playlist's snapshot_id and a representative vector using pgvector) in the database, so that it can be used for subsequent similarity comparisons and recommendations [user_input_file_1 source: 1019].
**Acceptance Criteria (ACs):**
1. The system `shall` take as input the comprehensive track data (including all specified audio features, as collected in Story 2.1) and the detailed artist/genre profiles (as compiled in Story 2.2) for a specific Spotify playlist [user_input_file_1 source: 1020].
2. The system `shall` compute aggregated statistics for key numerical audio features and track popularity across all tracks in the playlist [user_input_file_1 source: 1021].
3. This `shall` include at least the mean, median, and standard deviation for features like danceability, energy, valence, tempo, acousticness, instrumentalness, speechiness, loudness, and popularity [user_input_file_1 source: 1022].
4. The system `shall` identify and store the top N (e.g., N=10, this value should be configurable) most frequent artists and top N (e.g., N=10, configurable) most frequent genres, along with their respective counts or proportions, that contribute to the playlist's composition [user_input_file_1 source: 1023].
5. The system `shall` calculate and store a distribution of track release years for the playlist (e.g., count of tracks per year, or top 5 most frequent years with their proportions) [user_input_file_1 source: 1024].
6. The system `shall` compute a representative 'Playlist DNA vector' for the entire playlist, defined as the centroid (mean vector) of the numerical audio features and track popularity (as listed in AC2) of all its tracks [user_input_file_1 source: 1025].
7. The complete "Playlist DNA" profile – which includes the aggregated audio feature/metadata statistics (AC2), top artists (AC3), top genres (AC3), release year distribution (AC4), the 'Playlist DNA vector' (AC5), and the Spotify snapshot_id of the playlist at the time of this calculation – `shall` be durably stored in the configured PostgreSQL database (e.g., PostgreSQL, using JSONB for the profile data and a vector field for the DNA vector using pgvector) [user_input_file_1 source: 1026].
8. The database schema for storing the "Playlist DNA" (e.g., a dedicated table linking to the playlist, with columns for snapshot_id, a JSONB field for aggregated textual/statistical data, and a vector field for the DNA vector using pgvector) `shall` be defined and implemented via a schema migration (using the tool established in Story 1.3, e.g., Alembic) [user_input_file_1 source: 1027].
9. The process of computing and storing each "Playlist DNA" profile `shall` be logged with key information, such as the playlist ID processed, the snapshot_id, confirmation of success or details of any failure, and the time taken for computation [user_input_file_1 source: 1028].

## Story 2.4: "Playlist DNA" Update Logic & Orchestration
**User Story:** As the System, I want to implement logic that ensures the "Playlist DNA" is accurately updated by triggering a full re-computation (Stories 2.1-2.3) whenever the underlying Spotify playlist's content changes (detected via snapshot_id comparison during a sync operation), so that all analyses are always based on the most current playlist characteristics [user_input_file_1 source: 1029].
**Acceptance Criteria (ACs):**
1. As part of any user-initiated playlist synchronization process (which will be fully detailed in Epic 5), the system `shall` first retrieve the current snapshot_id for the managed Spotify playlist using the Spotify API [user_input_file_1 source: 1030].
2. The system `shall` compare this newly fetched snapshot_id with the snapshot_id currently stored in the database alongside the existing "Playlist DNA" profile for that playlist (as stored in Story 2.3) [user_input_file_1 source: 1031].
3. If no "Playlist DNA" profile exists in the database for the playlist, OR if the fetched snapshot_id from Spotify differs from the snapshot_id stored in the database, the system `shall` initiate a full re-computation of the "Playlist DNA" [user_input_file_1 source: 1032].
4. This re-computation process involves sequentially executing the functionalities defined in Story 2.1 (Fetch Full Track Details), Story 2.2 (Extract Artist & Genre Profiles), and Story 2.3 (Compute & Store "Playlist DNA" Profile) [user_input_file_1 source: 1033].
5. If the fetched snapshot_id is identical to the snapshot_id stored in the database, the system `shall` skip the full re-computation of the "Playlist DNA" during that specific sync cycle [user_input_file_1 source: 1034].
6. This is to conserve processing resources and Spotify API calls, assuming the playlist has not changed [user_input_file_1 source: 1035].
7. Upon successful re-computation and storage of an updated "Playlist DNA" profile (as per Story 2.3), the new, current snapshot_id from Spotify `shall` be stored in the database along with the updated DNA profile [user_input_file_1 source: 1036].
8. The outcome of the snapshot_id comparison (i.e., whether a DNA re-computation was triggered or skipped) `shall` be logged by the system for auditing and debugging purposes [user_input_file_1 source: 1037].