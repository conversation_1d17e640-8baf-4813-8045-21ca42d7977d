# Frontend State Management In-Depth (Pinia)

This section expands on the State Management strategy using **Pinia (Latest Stable)**, as chosen in the main Architecture Document, for the Playlist Intelligence Agent MVP.

* **Chosen Solution:** Pinia.
* **Decision Guide for State Location:**
    * **Global/Feature State (Pinia Stores):** Data shared across multiple unrelated components or views; data that persists across route changes; complex state logic requiring actions/getters. **MUST be used for session data (auth status, user info, managed playlist summary), application-wide notifications, queue data (recommendations, removals), and sync operation status.**
    * **Local Component State (Vue Composition API: `ref`, `reactive`):** UI-specific state, not needed outside the component or its direct children (e.g., form input values, dropdown open/close status, `isJustificationVisible` in `SongReviewCard`). **MUST be the default choice unless criteria for Pinia store are met.**

## Store Structure / Slices

Pinia stores will be modular, typically defined per feature. Main store setup in `src/store/index.ts`. Feature stores like `src/features/auth/AuthStore.ts` or `src/store/authStore.ts`.

* **Core Store Example: `AuthStore` (e.g., in `src/features/auth/AuthStore.ts` or `src/store/authStore.ts`):**
    * **Purpose:** Manages user session, authentication status, and basic user profile information accessible globally. Based on API DTO `AuthStatusResponseDto`.
    * **State Shape (Interface/Type using `defineStore`):**
        ```typescript
        // In src/types/index.ts or src/features/auth/types.ts
        export interface UserSummary { 
          spotifyUserId: string;
          displayName: string;
        }
        export interface ManagedPlaylistSummary { 
          id: string;
          spotifyPlaylistId: string;
          name: string;
        }
        export interface AuthState {
          isAuthenticated: boolean;
          currentUser: UserSummary | null;
          managedPlaylistSummary: ManagedPlaylistSummary | null; // From AuthStatusResponse
          status: 'idle' | 'loading' | 'succeeded' | 'failed';
          error: string | null;
        }

        // In AuthStore.ts
        import { defineStore } from 'pinia';
        import type { AuthState } from '@/types'; // Adjust path as needed
        import { authService } from '@/services/authService'; // Example service

        export const useAuthStore = defineStore('auth', {
          state: (): AuthState => ({
            isAuthenticated: false,
            currentUser: null,
            managedPlaylistSummary: null,
            status: 'idle',
            error: null,
          }),
          getters: {
            isUserAuthenticated: (state): boolean => state.isAuthenticated,
            userDisplayName: (state): string => state.currentUser?.displayName ?? 'Guest',
            activeManagedPlaylistId: (state): string | null => state.managedPlaylistSummary?.spotifyPlaylistId ?? null,
          },
          actions: {
            async checkAuthStatus() {
              this.status = 'loading';
              this.error = null;
              try {
                const response = await authService.getAuthStatus(); // Assuming authService.getStatus() returns AuthStatusResponseDto
                this.isAuthenticated = response.isAuthenticated;
                this.currentUser = response.user;
                this.managedPlaylistSummary = response.managedPlaylist;
                this.status = 'succeeded';
              } catch (err) {
                this.isAuthenticated = false;
                this.currentUser = null;
                this.managedPlaylistSummary = null;
                this.error = err instanceof Error ? err.message : 'Failed to check auth status';
                this.status = 'failed';
              }
            },
            async performLogout() {
              this.status = 'loading';
              try {
                await authService.logout(); // Calls POST /api/v1/auth/logout
                this.$reset(); // Resets store to initial state
                this.status = 'idle';
              } catch (err) {
                 // Even if logout fails, clear client state
                this.$reset();
                this.error = err instanceof Error ? err.message : 'Logout failed';
                this.status = 'failed'; // Or 'idle' if preferred after clearing
              }
            },
          }
        });
        ```
    * **Key Getters:** `isUserAuthenticated`, `currentUser`, `managedPlaylistSummary`, `activeManagedPlaylistId`.
    * **Key Actions:** `checkAuthStatus` (async, calls backend API `GET /api/v1/auth/status`), `performLogout` (async, calls backend `POST /api/v1/auth/logout`). Async actions MUST manage `status` (`idle`, `loading`, `succeeded`, `failed`) and `error` properties to provide UI feedback, aligning with UI/UX Spec for modals/indicators.

* **Feature Store Template: `use[FeatureName]Store` (e.g., `ReviewQueueStore.ts`):**
    * **Purpose:** {To be filled out when a new feature requires its own state slice e.g., managing discovery/removal queue items, current sync job status.}
    * **State Shape:** {To be defined by the feature, including properties for data, loading status (e.g., `queueStatus: 'idle' | 'loading' | 'succeeded' | 'failed'`), and errors (`queueError: string | null`). This pattern is crucial for driving UI feedback.}
    * **Key Getters:** {To be defined by the feature.}
    * **Key Actions:** {To be defined by the feature, including async actions for API calls. Async actions MUST manage loading/error states consistently following the pattern shown in `AuthStore`.}
    * **Export:** All stores MUST be exported for use in components.

## Key Selectors (Getters)

(In Pinia, these are **Getters**. They are memoized by default.)

* `isUserAuthenticated` (from `AuthStore`)
* `currentManagedPlaylistName` (from `AuthStore` or a dedicated `PlaylistStore`)
* `discoveryQueueCount` (from `ReviewQueueStore` or `SyncStore`)

## Key Actions / Reducers / Thunks (Actions)

(In Pinia, these are **Actions**. Asynchronous actions are natively supported.)

* **Core Action Example: `checkAuthStatus()` (in `AuthStore`):**
    * **Purpose:** Fetches the user's current authentication status and basic profile from the backend.
    * **API Call:** `GET /api/v1/auth/status`
    * **State Updates:** Updates `isAuthenticated`, `currentUser`, `managedPlaylistSummary`, `status`, `error` in the `AuthStore`.
* **Feature Action Template: `Workspace[FeatureData]()` (in `use[FeatureName]Store`):**
    * **Purpose:** {To be defined, e.g., fetch items for a review queue.}
    * **API Call:** {Specify backend endpoint, e.g., `GET /api/v1/reviews/recommendations`}
    * **State Updates:** {How it updates the feature store's state on pending, success, error, following the `status` and `error` pattern established in `AuthStore`.}