# Key References

This document contains important links, API documentation references, external service documentation, and other key resources for the Playlist Intelligence Agent project.

## External APIs and Services

### Spotify Web API
* **Official Documentation**: https://developer.spotify.com/documentation/web-api
* **Authentication Guide**: https://developer.spotify.com/documentation/web-api/concepts/authorization
* **Scopes Reference**: https://developer.spotify.com/documentation/web-api/concepts/scopes
* **Rate Limiting**: https://developer.spotify.com/documentation/web-api/concepts/rate-limits
* **Python Library (Spotipy)**: https://spotipy.readthedocs.io/

### PostgreSQL and pgvector
* **PostgreSQL Documentation**: https://www.postgresql.org/docs/
* **pgvector Extension**: https://github.com/pgvector/pgvector
* **Vector Similarity Search**: https://github.com/pgvector/pgvector#querying

## Framework and Library Documentation

### Backend (Python/FastAPI)
* **FastAPI Documentation**: https://fastapi.tiangolo.com/
* **FastAPI Tutorial**: https://fastapi.tiangolo.com/tutorial/
* **Pydantic Documentation**: https://docs.pydantic.dev/
* **Pydantic Settings**: https://docs.pydantic.dev/latest/concepts/pydantic_settings/
* **SQLAlchemy 2.0 Documentation**: https://docs.sqlalchemy.org/en/20/
* **Alembic Documentation**: https://alembic.sqlalchemy.org/
* **Uvicorn Documentation**: https://www.uvicorn.org/
* **AsyncPG Documentation**: https://magicstack.github.io/asyncpg/
* **python-jose Documentation**: https://python-jose.readthedocs.io/
* **Cryptography Documentation**: https://cryptography.io/

### Frontend (Vue.js/TypeScript)
* **Vue.js 3 Documentation**: https://vuejs.org/guide/
* **Vue.js Composition API**: https://vuejs.org/guide/extras/composition-api-faq.html
* **Vite Documentation**: https://vitejs.dev/guide/
* **TypeScript Documentation**: https://www.typescriptlang.org/docs/
* **Vitest Documentation**: https://vitest.dev/guide/

### Development Tools
* **uv Documentation**: https://docs.astral.sh/uv/
* **Ruff Documentation**: https://docs.astral.sh/ruff/
* **Docker Documentation**: https://docs.docker.com/
* **Docker Compose Documentation**: https://docs.docker.com/compose/

## Standards and Best Practices

### Python
* **PEP 8 Style Guide**: https://peps.python.org/pep-0008/
* **Python Type Hints**: https://docs.python.org/3/library/typing.html
* **Python Logging**: https://docs.python.org/3/library/logging.html
* **Python asyncio**: https://docs.python.org/3/library/asyncio.html

### TypeScript/JavaScript
* **TypeScript Handbook**: https://www.typescriptlang.org/docs/handbook/
* **ESLint Rules**: https://eslint.org/docs/rules/
* **Prettier Configuration**: https://prettier.io/docs/en/configuration.html

### Security
* **OWASP Top 10**: https://owasp.org/www-project-top-ten/
* **JWT Best Practices**: https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/
* **OAuth 2.0 Security**: https://datatracker.ietf.org/doc/html/rfc6749

## Development Environment

### Package Managers
* **uv (Python)**: https://docs.astral.sh/uv/
* **npm (Node.js)**: https://docs.npmjs.com/

### Database Tools
* **pgAdmin**: https://www.pgadmin.org/docs/
* **DBeaver**: https://dbeaver.io/docs/

### IDE and Editor Extensions
* **VS Code Python Extension**: https://marketplace.visualstudio.com/items?itemName=ms-python.python
* **VS Code Vue Extension**: https://marketplace.visualstudio.com/items?itemName=Vue.volar
* **VS Code TypeScript Extension**: Built-in

## Hosting and Deployment

### Container Platforms
* **Docker Hub**: https://hub.docker.com/
* **PostgreSQL Docker Image**: https://hub.docker.com/_/postgres
* **pgvector Docker Image**: https://hub.docker.com/r/pgvector/pgvector

### Hosting Platforms
* **Koyeb Documentation**: https://www.koyeb.com/docs
* **Vercel Documentation**: https://vercel.com/docs
* **Netlify Documentation**: https://docs.netlify.com/

## Project-Specific Resources

### Internal Documentation
* [Project Structure](./project-structure.md)
* [Technology Stack](./tech-stack.md)
* [Environment Variables](./environment-vars.md)
* [Operational Guidelines](./operational-guidelines.md)

### Repository
* **GitHub Repository**: https://github.com/patrickronn/playlist-intelligence-agent
* **Issues and Project Management**: GitHub Issues and Projects

## Useful Tools and Utilities

### API Testing
* **Postman**: https://www.postman.com/
* **HTTPie**: https://httpie.io/
* **curl Documentation**: https://curl.se/docs/

### Database Management
* **Alembic Commands**: https://alembic.sqlalchemy.org/en/latest/tutorial.html
* **PostgreSQL Commands**: https://www.postgresql.org/docs/current/app-psql.html

### Monitoring and Debugging
* **FastAPI Automatic Documentation**: Available at `/docs` endpoint in development
* **Python Logging Best Practices**: https://docs.python.org/3/howto/logging.html