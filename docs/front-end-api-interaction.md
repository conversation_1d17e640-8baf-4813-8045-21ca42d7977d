# Frontend API Interaction Layer

Communication with the backend FastAPI will primarily use the browser's native **Fetch API**, wrapped in lightweight service modules located in `src/services/` for better organization, reusability, and to centralize API logic. Since authentication is handled by HttpOnly cookies, the frontend typically won't need to manage or attach JWTs to requests.

## Client/Service Structure

* **HTTP Client Setup (`src/services/apiClient.ts`):**
    * A utility module, `apiClient.ts`, will provide a wrapper around the native `Workspace` API.
    * **Responsibilities:**
        * Prepend the API base URL (from `import.meta.env.VITE_API_BASE_URL`) to all requests.
        * Set default headers for `POST`/`PUT` requests, such as `Content-Type: 'application/json'`.
        * Automatically parse JSON responses using `response.json()`.
        * Perform basic error checking: if `!response.ok`, it will parse the JSON error body (if any, expecting `{"detail": "error message"}` format from backend), and then throw a custom `ApiError` object. This `ApiError` should contain the original response status, the error message from `detail`, and potentially the full error response data. This allows service functions and UI layers to catch and handle API errors more gracefully.
        * It will NOT handle JWT token attachment as tokens are HttpOnly cookies.
* **Service Definitions (Example):** All service functions will be asynchronous and return Promises. They will use the `apiClient.ts` for actual `Workspace` calls.
    * **`authService.ts` (in `src/services/authService.ts`):**
        * **Purpose:** Handles API interactions related to authentication.
        * **Functions:**
            * `getAuthStatus(): Promise<AuthStatusResponseDto>` (Calls `GET /api/v1/auth/status`)
            * `triggerLogin(): void` (Redirects browser to `GET /api/v1/auth/login` endpoint on the backend)
            * `logout(): Promise<LogoutResponseDto>` (Calls `POST /api/v1/auth/logout`)
    * **`playlistService.ts` (in `src/services/playlistService.ts`):**
        * **Purpose:** Handles API interactions related to playlist configuration and data.
        * **Functions:**
            * `getUserPlaylists(): Promise<UserPlaylistsResponseDto>` (Calls `GET /api/v1/playlists`)
            * `setManagedPlaylist(spotifyPlaylistId: string): Promise<SetManagedPlaylistResponseDto>` (Calls `POST /api/v1/playlists/managed`)
            * `getManagedPlaylistDetails(): Promise<GetManagedPlaylistResponseDto>` (Calls `GET /api/v1/playlists/managed`)
    * **(Other services like `syncService.ts`, `reviewService.ts`, `debugService.ts` will be structured similarly, mapping to API groups in Main Arch Doc Section 8)**

## Error Handling & Retries (Frontend)

* **Global Error Handling:** Service functions (using `apiClient.ts`) will throw `ApiError` on API failures. Pinia actions calling these services will catch these errors, update store `status` to `'failed'`, and store the `error` message. Vue components can then react to these store states. A global Vue error handler (`app.config.errorHandler`) can be set up in `main.ts` to catch unhandled promise rejections or other critical frontend errors, potentially displaying a generic error toast/notification to the user and logging to a monitoring service if configured.
* **Specific Error Handling:** Components or Pinia actions SHOULD provide specific user feedback for API errors where relevant (e.g., "Failed to load playlist. Please try again." on the Playlist Selection screen, as per UI/UX Spec).
* **Retry Logic:** Client-side retry logic is NOT planned for MVP. The backend handles retries for its external API calls (e.g., to Spotify or LLM).