# Component View and Design Patterns

This document describes the initial architectural design patterns adopted for the backend monolith and details the major logical components of the backend, their primary responsibilities, and how they collaborate.

## 1. Initial Architectural / Design Patterns Adopted (Backend Monolith Focus)
(Extracted from Main Architecture Document Section 4 [user_input_file_0 source: 37])

To ensure the backend monolith is "well-designed" and supports the preference for components testable in isolation, the following foundational patterns will be adopted [user_input_file_0 source: 37]:
* **Modular Monolith:** The backend will be structured into distinct modules with clear responsibilities [user_input_file_0 source: 37].
* **Service Layer (Use Case Driven):** Business logic will be encapsulated within service classes/modules [user_input_file_0 source: 38].
* **Repository Pattern:** Data access logic will be abstracted behind repository interfaces (likely using SQLAlchemy) [user_input_file_0 source: 39].
* **Dependency Injection:** FastAPI's support for DI will be leveraged for loose coupling and testability [user_input_file_0 source: 40].
* **Configuration Management:** Application configuration loaded via a dedicated module (e.g., `backend/src/app/config.py`) from environment variables and/or `.env` files, providing centralized access to settings [user_input_file_0 source: 41].
* **Snapshot-based Processing for DNA & Suggestions:** Core logic operates on playlist data snapshots [user_input_file_0 source: 42].

## 2. Component View (Backend)
(Extracted from Main Architecture Document Section 5 [user_input_file_0 source: 43])

This section details the major logical components of the backend monolith, their primary responsibilities, and how they collaborate [user_input_file_0 source: 43].

### 2.1. Identified Backend Components & Primary Responsibilities
* **ConfigurationService**: Loads, manages, and provides access to all application configurations [user_input_file_0 source: 44].
* **SpotifyIntegrationService**: Encapsulates all direct communication with the Spotify Web API (auth, data fetching, playlist modification) [user_input_file_0 source: 45].
* **LLMIntegrationService**: Manages all interactions with the chosen LLM API (prompt formatting, API calls, responses) [user_input_file_0 source: 46].
* **PersistenceLayer (Repositories)**: Abstracts all database interactions (CRUD for PostgreSQL + pgvector, likely via SQLAlchemy) [user_input_file_0 source: 47].
* **PlaylistDNACalculationService**: Orchestrates fetching playlist data, computes "Playlist DNA," stores it, and manages `snapshot_id` logic [user_input_file_0 source: 48].
* **NewReleaseDiscoveryService**: Discovers new releases, filters, scores similarity against DNA, coordinates LLM justifications, stores pending recommendations [user_input_file_0 source: 49].
* **RemovalSuggestionService**: Detects vibe outliers, applies filtering rules, coordinates LLM justifications, stores pending removal suggestions [user_input_file_0 source: 50].
* **SyncOrchestrationService**: Manages the overall "Refresh Discoveries" (manual sync) process, coordinating DNA, Discovery, and Suggestion services, and reporting progress [user_input_file_0 source: 51].
* **APIControllerLayer (FastAPI Routers)**: Exposes HTTP endpoints for the frontend, handles request validation, authentication, calls services, formats responses, uses centralized error handling [user_input_file_0 source: 52].
* **UserActionService (also referred to as ApplyChangesService in workflow discussions)**: Processes user decisions from review queues, and orchestrates applying approved changes to Spotify [user_input_file_0 source: 53].

### 2.2. Frontend-Backend Secure Communication
All communication *must* use HTTPS [user_input_file_0 source: 54]. After initial Spotify OAuth via the backend, the backend *shall* issue its own application-specific, short-lived JSON Web Token (JWT) [user_input_file_0 source: 55]. This JWT *shall* be transmitted via secure, HttpOnly, SameSite cookies [user_input_file_0 source: 56]. The APIControllerLayer *shall* validate this JWT on incoming requests to protected endpoints [user_input_file_0 source: 57].

### 2.3. Component Collaborations
The major backend components, as identified in Section 2.1, collaborate to fulfill the application's use cases [user_input_file_0 source: 58]. The primary interaction patterns are [user_input_file_0 source: 59]:
* The APIControllerLayer receives incoming HTTP requests from the frontend [user_input_file_0 source: 59].
* For complex operations like data synchronization or applying playlist changes, the APIControllerLayer delegates tasks to specialized orchestrator services such as the `SyncOrchestrationService` or the `ApplyChangesService` (part of `UserActionService`) [user_input_file_0 source: 60].
* These orchestrator services coordinate the "engine" services (e.g., `PlaylistDNACalculationService`, `NewReleaseDiscoveryService`, `RemovalSuggestionService`) [user_input_file_0 source: 61].
* The engine services, in turn, utilize foundational services like `SpotifyIntegrationService` (for Spotify API calls), `LLMIntegrationService` (for LLM interactions), the `PersistenceLayer` (for database operations), and the `ConfigurationService` (for application settings) [user_input_file_0 source: 62].
* Detailed illustrations of these collaborations for key workflows, such as the "Manual Sync Process," "Review Queues & Action Flow," and "Apply Approved Changes to Spotify Flow," are provided with sequence diagrams in `docs/sequence-diagrams.md` (originally Section 10 of Main Architecture Document) [user_input_file_0 source: 63]. These diagrams visually represent the call sequences and data exchanges between components [user_input_file_0 source: 64].