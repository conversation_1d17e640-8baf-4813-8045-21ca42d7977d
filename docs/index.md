# Project Documentation Index

This index provides a central catalog for all key documentation related to the Playlist Intelligence Agent MVP project.

## Quick Start

### Essential Documentation
* **[Technology Stack](./tech-stack.md)** - Complete list of technologies, frameworks, and versions used
* **[Project Structure](./project-structure.md)** - Monorepo organization and directory layout
* **[Environment Variables](./environment-vars.md)** - Configuration management and security
* **[Operational Guidelines](./operational-guidelines.md)** - Development workflow, coding standards, and best practices
* **[Key References](./key-references.md)** - Important external documentation and resources

### Getting Started
1. Review the [Project Structure](./project-structure.md) to understand the codebase organization
2. Set up your environment using [Environment Variables](./environment-vars.md) documentation
3. Follow the [Operational Guidelines](./operational-guidelines.md) for development standards
4. Reference the [Technology Stack](./tech-stack.md) for specific versions and tools

## Product Requirements & Epics

### [Epic 1: Project Foundation & Core Spotify Integration](./epic-1.md)
Defines the foundational project structure, API scaffolding, initial database setup, core Spotify authentication, and basic playlist data retrieval capabilities.

### [Epic 2: "Playlist DNA" Engine & Profile Generation](./epic-2.md)
Details the functionality to analyze a connected Spotify playlist, extract comprehensive track data, compute a "Playlist DNA" profile, and store it.

### [Epic 3: New Release Discovery & Recommendation Engine](./epic-3.md)
Covers the discovery of new music releases, filtering for relevance and availability, similarity scoring against "Playlist DNA," and AI-powered justification generation for recommendations.

### [Epic 4: Removal Suggestion Engine](./epic-4.md)
Outlines the system's ability to analyze the current playlist for removal candidates (vibe outliers, age), generate AI justifications, and respect protection periods.

### [Epic 5: Curation Web Dashboard & Playlist Update Orchestration](./epic-5.md)
Describes the interactive web UI for reviewing recommendations/removals, triggering syncs, and applying changes to the Spotify playlist.

## System Architecture Details

### [API Reference](./api-reference.md)
Details the internal APIs exposed by the Python/FastAPI backend for consumption by the Vue.js frontend, including endpoint definitions, request/response schemas, and authentication requirements.

### [Component View and Design Patterns (Backend)](./component-view.md)
Describes the initial architectural design patterns adopted for the backend monolith and details the major logical backend components, their responsibilities, and collaborations.

### [Data Models](./data-models.md)
Outlines primary data structures, including core application entities, API payload DTOs, and detailed PostgreSQL database table schemas.

### [Environment Variables Management Strategy](./environment-vars.md)
Describes the strategy for managing environment variables and sensitive configurations, including local development and production deployment.

### [Infrastructure and Deployment Overview](./infra-deployment.md)
Outlines infrastructure, hosting choices, core services used, IaC approach (Docker focus for MVP), and deployment/rollback strategies.

### [Key Reference Documents](./key-references.md)
Comprehensive collection of external documentation, API references, and important resources for development.

### [Operational Guidelines](./operational-guidelines.md)
Consolidates key operational guidelines including Error Handling Strategy, Coding Standards (covering both backend and frontend), Overall Testing Strategy, and Security Best Practices for the project.

### [Project Structure (Monorepo)](./project-structure.md)
Details the Monorepo structure for the backend, frontend, shared documentation, and scripts, including an ASCII diagram and key directory descriptions.

### [Sequence Diagrams](./sequence-diagrams.md)
Illustrates core workflows and component interactions within the system using Mermaid sequence diagrams, including Manual Sync, Review Queues, Playlist Configuration, and Apply Changes flows.

### [Technology Stack](./tech-stack.md)
Lists the definitive technology choices for the project, including languages, runtimes, frameworks, databases, libraries, and tools, with specified versions.

## Frontend Architecture Details

### [Frontend Project Structure](./front-end-project-structure.md)
Details the specific internal directory structure for the `frontend/` application (Vue.js with Vite), including organization of assets, components, features, router, services, store, and types.

### [Frontend Styling Approach and Guide](./front-end-style-guide.md)
Outlines the core styling philosophy for the frontend, focusing on Tailwind CSS usage, configuration, and key conventions. References the main UI/UX Specification for broader visual design.

### [Frontend Component Guide](./front-end-component-guide.md)
Describes conventions for naming, organizing, and specifying UI components, including a detailed template for component specifications.

### [Frontend Coding Standards (Specifics & Pointers)](./front-end-coding-standards.md)
Highlights frontend-specific coding emphases and points to the comprehensive frontend coding standards within the operational guidelines.

### [Frontend State Management In-Depth (Pinia)](./front-end-state-management.md)
Expands on the State Management strategy using Pinia, covering store structure, core store examples, feature store templates, getters, and action patterns.

### [Frontend API Interaction Layer](./front-end-api-interaction.md)
Details how the frontend communicates with backend APIs using the native Fetch API, service module structure, and error handling.

### [Frontend Routing Strategy (Vue Router)](./front-end-routing-strategy.md)
Outlines frontend navigation using Vue Router, including route definitions for all views and authentication guard mechanisms.

### [Frontend Testing Strategy (Specifics)](./front-end-testing-strategy.md)
Elaborates on the frontend aspects of the overall testing strategy, focusing on component and feature/flow testing using Vitest and Vue Test Utils.