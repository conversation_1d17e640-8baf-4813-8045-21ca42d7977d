# Data Models

This section outlines the primary data structures used within the Playlist Intelligence Agent MVP, including core application entities, API payload schemas (Data Transfer Objects - DTOs), and database table schemas[cite: 198].

## 1. Core Application Entities / Domain Objects
This subsection outlines the primary entities or domain objects that encapsulate the data and core concepts within the Playlist Intelligence Agent's backend logic[cite: 199]. These entities are used by the service layer and often map to database schemas and API DTOs[cite: 200].

### 1.1. User
* **Description:** Represents the application's authenticated user, primarily their link to a Spotify account and the necessary credentials to act on their behalf[cite: 200]. This entity is central to all user-specific operations and data[cite: 201].
* **Key Attributes/Properties:**
    * `internalId`: A unique identifier for the user within this application (corresponds to `users.id` in the database)[cite: 202].
    * `spotifyUserId`: The user's unique identifier on Spotify (e.g., `users.spotify_user_id`)[cite: 203].
    * `spotifyDisplayName`: The user's display name on Spotify (e.g., `users.spotify_display_name`)[cite: 203].
    * `accessToken`: The current, valid (and decrypted) Spotify access token[cite: 204].
    * `refreshToken`: The Spotify refresh token (decrypted), used to obtain new access tokens[cite: 204].
    * `tokenExpiresAt`: The timestamp indicating when the current `accessToken` expires[cite: 205].
* **Key Behaviors/Responsibilities (Conceptual):**
    * Knowing if its Spotify tokens are valid/expired[cite: 205].
    * Potentially having methods to refresh its own tokens (though this logic might reside in an AuthService)[cite: 206].
    * Providing access to its Spotify credentials for services that need to interact with the Spotify API[cite: 207].

### 1.2. ManagedPlaylist
* **Description:** Represents the specific Spotify playlist that the user has configured for the Playlist Intelligence Agent to manage[cite: 208]. It holds identifying information for the playlist, its current state as known to the system (like snapshot_id), and a link to its "Playlist DNA"[cite: 209].
* **Key Attributes/Properties:**
    * `internalId`: A unique identifier for this managed playlist record within the application (corresponds to `managed_playlists.id`)[cite: 210].
    * `userId`: The internal ID of the User who owns/manages this playlist within the application[cite: 211].
    * `spotifyPlaylistId`: The unique identifier for this playlist on Spotify (e.g., `managed_playlists.spotify_playlist_id`)[cite: 212].
    * `name`: The current name of the playlist on Spotify[cite: 212].
    * `description`: The current description of the playlist from Spotify[cite: 213].
    * `ownerDisplayName`: The display name of the Spotify user who owns the playlist[cite: 213].
    * `trackCount`: The current number of tracks in the playlist on Spotify[cite: 214].
    * `spotifySnapshotId`: The latest known `snapshot_id` from Spotify for this playlist, used to detect changes[cite: 215].
    * `lastSyncedAt`: Timestamp indicating when the full synchronization process (DNA update, discovery, suggestions) was last successfully completed for this playlist[cite: 216].
    * `isActive`: A boolean indicating if this is the currently active playlist being managed by the agent for the user (for MVP, this will always be true for the single configured playlist)[cite: 217].
    * `currentPlaylistDnaId`: An optional reference to the internal ID of the `PlaylistDNA` entity that corresponds to the current `spotifySnapshotId` of this playlist[cite: 218].
* **Key Behaviors/Responsibilities (Conceptual):**
    * Holding all necessary identifiers and metadata for interacting with the specific Spotify playlist[cite: 219].
    * Knowing its current `snapshot_id` to help services determine if a "Playlist DNA" recalculation is needed[cite: 220].
    * Being associated with its corresponding `PlaylistDNA` entity[cite: 221].

### 1.3. PlaylistDNA
* **Description:** Represents the calculated "Playlist DNA" profile for a specific snapshot of a `ManagedPlaylist`[cite: 221]. It includes aggregated audio feature statistics, lists of top artists and genres, release year distributions, and the representative numerical vector used for similarity comparisons[cite: 222]. This entity is fundamental to the agent's recommendation and removal suggestion logic (PRD Story 2.3)[cite: 223].
* **Key Attributes/Properties:**
    * `internalId`: A unique identifier for this DNA record within the application (corresponds to `playlist_dna.id`)[cite: 224].
    * `managedPlaylistId`: The internal ID of the `ManagedPlaylist` to which this DNA profile belongs[cite: 225].
    * `spotifySnapshotId`: The `snapshot_id` from Spotify of the `ManagedPlaylist` at the time this DNA was computed[cite: 226].
    * `dnaSummary`: An object containing the human-readable summary components of the DNA, such as[cite: 227]:
        * `topArtists`: A list of objects, each with artist name (e.g., `[{ "name": "Artist A" }, ...]`)[cite: 227].
        * `topGenres`: A list of strings representing top genres (e.g., `["Indie Pop", "Dream Pop"]`)[cite: 228].
        * `keyAudioFeatures`: A list of objects, each describing a key audio feature's average value and a qualitative assessment (e.g., `[{ "featureName": "energy", "averageValue": 0.75, "qualitativeDescription": "High" }, ...]`)[cite: 229].
        * `releaseYearDistribution`: An object or list representing the distribution of track release years[cite: 230].
    * `dnaVector`: An array of numbers representing the N-dimensional numerical vector (centroid) of the playlist's core audio features[cite: 231]. This vector is used in similarity calculations[cite: 232].
    * `calculatedAt`: Timestamp indicating when this DNA profile was computed[cite: 232].
* **Key Behaviors/Responsibilities (Conceptual):**
    * Providing access to the summarized and vector components of a playlist's musical profile[cite: 233].
    * Being intrinsically linked to a specific `spotifySnapshotId` of its parent `ManagedPlaylist`[cite: 234].

### 1.4. PendingRecommendation
* **Description:** Represents a song that has been identified by the system as a potential good addition to the `ManagedPlaylist`[cite: 235]. It includes the song's metadata, the AI-generated justification for the recommendation, its similarity score, and its current review status[cite: 236]. This entity supports PRD Story 3.5 and the Discovery Queue functionality[cite: 237].
* **Key Attributes/Properties:**
    * `internalId`: A unique identifier for this recommendation record (corresponds to `pending_recommendations.id`)[cite: 238].
    * `managedPlaylistId`: The internal ID of the `ManagedPlaylist` this recommendation is for[cite: 239].
    * `spotifyTrackId`: The unique identifier for the recommended track on Spotify[cite: 240].
    * `trackName`: The name of the recommended track[cite: 240].
    * `artists`: A list of objects, each representing an artist of the track (e.g., `[{ "name": "Artist Name" }]`)[cite: 241].
    * `albumName`: The name of the album the track belongs to[cite: 242].
    * `albumReleaseDate`: The release date of the album/track[cite: 242].
    * `previewUrl`: An optional URL to a short audio preview of the track from Spotify[cite: 243].
    * `spotifyTrackUrl`: A direct link to the full track on Spotify[cite: 244].
    * `justification`: The LLM-generated text explaining why this track is recommended[cite: 244].
    * `similarityScore`: An optional numerical score indicating the track's similarity to the Playlist DNA[cite: 245].
    * `status`: The current review status of this recommendation (e.g., `"pending_review"`, `"approved_for_addition"`, `"rejected"`, `"applied_to_spotify"`, `"failed_to_apply"`)[cite: 246]. This would align with the `recommendation_status` ENUM defined for the database[cite: 247].
    * `discoveredAt`: Timestamp indicating when this recommendation was discovered/generated by the system[cite: 248].
    * `actionedAt`: An optional timestamp indicating when the user last acted upon (approved/rejected) this recommendation[cite: 249].
* **Key Behaviors/Responsibilities (Conceptual):**
    * Encapsulating all relevant information for a song being recommended for addition[cite: 250].
    * Tracking its lifecycle through the user review and application process via its status[cite: 251].
    * Providing the necessary details for display in the "Discovery Queue"[cite: 252].

### 1.5. PendingRemovalSuggestion
* **Description:** Represents a song from the `ManagedPlaylist` that the system has flagged for potential removal based on predefined criteria (e.g., vibe deviation, release age)[cite: 253]. It includes the song's metadata, the AI-generated justification for the removal suggestion, the specific reason it was flagged, and its current review status[cite: 254]. This entity supports PRD Story 4.4 and the Removal Review Queue functionality[cite: 255].
* **Key Attributes/Properties:**
    * `internalId`: A unique identifier for this removal suggestion record (corresponds to `pending_removal_suggestions.id`)[cite: 256].
    * `managedPlaylistId`: The internal ID of the `ManagedPlaylist` from which this song is suggested for removal[cite: 257].
    * `spotifyTrackId`: The unique identifier for the track on Spotify that is being suggested for removal[cite: 258].
    * `trackName`: The name of the track[cite: 259].
    * `artists`: A list of objects, each representing an artist of the track (e.g., `[{ "name": "Artist Name" }]`)[cite: 259].
    * `albumName`: The name of the album the track belongs to[cite: 260].
    * `previewUrl`: An optional URL to a short audio preview of the track from Spotify[cite: 261].
    * `spotifyTrackUrl`: A direct link to the full track on Spotify[cite: 262].
    * `justification`: The LLM-generated text explaining why removal of this track is suggested[cite: 263].
    * `reasonForSuggestion`: The primary reason the track was flagged for removal (e.g., `"vibe_outlier"`, `"release_age_exceeded"`)[cite: 264]. This would align with the `removal_suggestion_reason` ENUM[cite: 264].
    * `deviationScore`: An optional numerical score indicating the track's deviation from the Playlist DNA, if the `reasonForSuggestion` is `"vibe_outlier"`[cite: 265].
    * `status`: The current review status of this removal suggestion (e.g., `"pending_review"`, `"remove_approved"`, `"kept_by_user"`, `"applied_to_spotify"`, `"failed_to_apply"`)[cite: 266]. This would align with the `removal_suggestion_status` ENUM[cite: 267].
    * `suggestedAt`: Timestamp indicating when this removal suggestion was generated by the system[cite: 267].
    * `actionedAt`: An optional timestamp indicating when the user last acted upon (approved removal/kept) this suggestion[cite: 268].
* **Key Behaviors/Responsibilities (Conceptual):**
    * Encapsulating all relevant information for a song being suggested for removal[cite: 269].
    * Tracking its lifecycle through the user review and application process via its status[cite: 270].
    * Providing the necessary details for display in the "Removal Review Queue"[cite: 271].

### 1.6. SyncLogEntry
* **Description:** Represents a log entry for a single execution of the full synchronization process[cite: 272]. It captures the status, timing, key metrics (like number of recommendations/removals generated, LLM calls and token counts), and any errors encountered during that specific sync operation[cite: 273]. This entity supports the debugging screen functionality (PRD Story 5.8 AC4) and operational monitoring (PRD NFR 1.4, NFR 3.3)[cite: 274].
* **Key Attributes/Properties:**
    * `internalId`: A unique identifier for this log entry record within the application (corresponds to `sync_log_entries.id`)[cite: 275].
    * `syncId`: The unique identifier assigned to this specific synchronization task when it was triggered[cite: 276].
    * `managedPlaylistId`: The internal ID of the `ManagedPlaylist` for which this sync was performed[cite: 277].
    * `status`: The final or current status of the sync operation (e.g., `"initiated"`, `"dna_analysis_started"`, `"discovery_started"`, `"suggestion_generation_started"`, `"completed_success"`, `"completed_with_errors"`, `"failed"`)[cite: 278]. This aligns with the `sync_status` ENUM[cite: 279].
    * `startedAt`: Timestamp indicating when the sync process began[cite: 279].
    * `completedAt`: An optional timestamp indicating when the sync process finished (either successfully or with failure)[cite: 280].
    * `durationMs`: An optional value indicating the total duration of the sync in milliseconds[cite: 281].
    * `playlistSnapshotIdBefore`: The `snapshot_id` of the `ManagedPlaylist` before this sync operation started[cite: 282].
    * `playlistSnapshotIdAfter`: The `snapshot_id` of the `ManagedPlaylist` after the DNA calculation stage of this sync operation[cite: 283].
    * `dnaRecalculated`: A boolean indicating whether the `PlaylistDNA` was re-calculated during this sync[cite: 284].
    * `recommendationsGeneratedCount`: The number of new song recommendations generated during this sync[cite: 285].
    * `removalsSuggestedCount`: The number of new removal suggestions generated during this sync[cite: 286].
    * `llmCallsMadeCount`: The total number of calls made to the LLM API during this sync[cite: 287].
    * `totalLlmTokensUsed`: The total number of LLM tokens (input + output) consumed during this sync[cite: 288].
    * `errorSummary`: An optional textual summary of any critical errors that occurred during the sync[cite: 289].
    * `details`: An optional object or structured data (e.g., JSON) containing more granular, stage-specific metrics or detailed error information from the sync process[cite: 290].
* **Key Behaviors/Responsibilities (Conceptual):**
    * Providing a comprehensive record of a single synchronization attempt[cite: 291].
    * Holding key performance and operational metrics for monitoring and debugging purposes[cite: 292].
    * Indicating the success or failure state of a sync and providing error details if applicable[cite: 293].

## 2. API Payload Schemas (DTOs)
This subsection defines the Data Transfer Objects used for request and response bodies in the API Reference (Section 8 of the Main Architecture Document)[cite: 293].

### 2.1. Authentication & User Context DTOs
* **AuthStatusResponseDto**
    * Description: DTO for the response of `GET /api/v1/auth/status`[cite: 295].
    * Schema Definition (TypeScript Interface): [cite: 296]
    ```typescript
    interface UserSummaryDto {
      spotifyUserId: string;
      displayName: string;
    }
    interface ManagedPlaylistSummaryDto {
      id: string; 
      spotifyPlaylistId: string;
      name: string;
    }
    interface AuthStatusResponseDto {
      isAuthenticated: boolean;
      user: UserSummaryDto | null;
      managedPlaylist: ManagedPlaylistSummaryDto | null; 
    }
    ```
* **LogoutResponseDto**
    * Description: DTO for the response of `POST /api/v1/auth/logout`[cite: 298].
    * Schema Definition (TypeScript Interface): [cite: 299]
    ```typescript
    interface LogoutResponseDto {
      message: string;
    }
    ```

### 2.2. Playlist Configuration DTOs
* **SpotifyPlaylistSummaryDto**
    * Description: Represents a summary of a Spotify playlist[cite: 300].
    * Schema Definition (TypeScript Interface): [cite: 301]
    ```typescript
    interface SpotifyPlaylistSummaryDto {
      id: string; // Spotify Playlist ID
      name: string; // Playlist Name
      trackCount: number; // Number of tracks
    }
    ```
* **UserPlaylistsResponseDto**
    * Description: DTO for `GET /api/v1/playlists` response[cite: 302].
    * Schema Definition (TypeScript Interface): [cite: 302]
    ```typescript
    interface UserPlaylistsResponseDto {
      playlists: SpotifyPlaylistSummaryDto[];
    }
    ```
* **SetManagedPlaylistRequestDto**
    * Description: DTO for `POST /api/v1/playlists/managed` request[cite: 303].
    * Schema Definition (TypeScript Interface): [cite: 303]
    ```typescript
    interface SetManagedPlaylistRequestDto {
      spotifyPlaylistId: string; // The Spotify ID of the playlist to be managed
    }
    ```
* **SetManagedPlaylistResponseDto**
    * Description: DTO for `POST /api/v1/playlists/managed` response[cite: 304].
    * Schema Definition (TypeScript Interface): [cite: 304]
    ```typescript
    interface SetManagedPlaylistResponseDto {
      message: string;
      managedPlaylist: {
        id: string; // Internal DB ID
        spotifyPlaylistId: string;
        name: string; // Name of the configured playlist
      };
    }
    ```
* **DnaSummaryDto**
    * Description: High-level summary of Playlist DNA[cite: 305].
    * Schema Definition (TypeScript Interface): [cite: 306]
    ```typescript
    interface TopArtistDto { name: string; }
    interface TopGenreDto { name: string; } 
    interface KeyAudioFeatureSummaryDto {
      featureName: string; // e.g., "Energy", "Valence", "Tempo"
      averageValue: number; // e.g., 0.75 or 120
      qualitativeDescription: string; // e.g., "High", "Positive"
    }
    interface DnaSummaryDto {
      lastCalculatedAt: string | null; // ISO 8601
      topArtists: TopArtistDto[];
      topGenres: TopGenreDto[]; 
      keyAudioFeatures: KeyAudioFeatureSummaryDto[];
    }
    ```
* **ManagedPlaylistDetailDto**
    * Description: Detailed info of managed playlist with DNA summary[cite: 308].
    * Schema Definition (TypeScript Interface): [cite: 309]
    ```typescript
    interface ManagedPlaylistDetailDto {
      id: string; // Internal DB ID
      spotifyPlaylistId: string;
      name: string;
      owner: string; // Playlist owner's display name
      description: string | null;
      trackCount: number;
      lastSyncedAt: string | null; // ISO 8601
      dnaSummary: DnaSummaryDto | null;
    }
    ```
* **GetManagedPlaylistResponseDto**
    * Description: DTO for `GET /api/v1/playlists/managed` response[cite: 311].
    * Schema Definition (TypeScript Interface): [cite: 311]
    ```typescript
    interface GetManagedPlaylistResponseDto {
      isConfigured: boolean;
      managedPlaylist: ManagedPlaylistDetailDto | null;
    }
    ```

### 2.3. Dashboard & Synchronization DTOs
* **QueueStatusResponseDto**
    * Description: DTO for `GET /api/v1/sync/queues/status` response[cite: 312].
    * Schema Definition (TypeScript Interface): [cite: 313]
    ```typescript
    interface QueueStatusResponseDto {
      discoveryQueueCount: number;
      removalQueueCount: number;
    }
    ```
* **SyncTriggerResponseDto**
    * Description: DTO for `POST /api/v1/sync/trigger` response[cite: 314].
    * Schema Definition (TypeScript Interface): [cite: 314]
    ```typescript
    interface SyncTriggerResponseDto {
      syncId: string; // Unique ID for the sync task
      message: string;
    }
    ```
* **SyncErrorDetailDto**
    * Description: Specific error during a sync process[cite: 315].
    * Schema Definition (TypeScript Interface): [cite: 316]
    ```typescript
    interface SyncErrorDetailDto {
      stage: string; // e.g., "DnaAnalysis", "Discovery"
      message: string;
    }
    ```
* **SyncStatusDetailsDto**
    * Description: Optional details in sync status response[cite: 317].
    * Schema Definition (TypeScript Interface): [cite: 317]
    ```typescript
    interface SyncStatusDetailsDto {
      recommendationsFound?: number | null;
      removalsSuggested?: number | null;
      errors?: SyncErrorDetailDto[];
    }
    ```
* **SyncStatusResponseDto**
    * Description: DTO for `GET /api/v1/sync/status/{sync_id}` response[cite: 318].
    * Schema Definition (TypeScript Interface): [cite: 319]
    ```typescript
    interface SyncStatusResponseDto {
      syncId: string;
      status: string; // e.g., "Pending", "Running_DnaAnalysis", "Completed_Success"
      progressMessage?: string | null;
      startedAt?: string | null; // ISO 8601
      completedAt?: string | null; // ISO 8601
      details?: SyncStatusDetailsDto | null;
    }
    ```

### 2.4. Review Queues & Actions DTOs
* **ArtistSummaryDto**
    * Description: Summary of an artist for track details[cite: 321].
    * Schema Definition (TypeScript Interface): [cite: 322]
    ```typescript
    interface ArtistSummaryDto {
      name: string;
    }
    ```
* **RecommendationItemDto**
    * Description: Single recommendation item[cite: 322].
    * Schema Definition (TypeScript Interface): [cite: 323]
    ```typescript
    interface RecommendationItemDto {
      id: string; // Internal DB ID
      spotifyTrackId: string;
      trackName: string;
      artists: ArtistSummaryDto[];
      albumName: string;
      albumReleaseDate: string; // ISO 8601 YYYY-MM-DD
      previewUrl?: string | null;
      spotifyTrackUrl: string;
      justification: string;
      similarityScore?: number | null;
      discoveredAt: string; // ISO 8601
    }
    ```
* **GetRecommendationsResponseDto**
    * Description: DTO for `GET /api/v1/reviews/recommendations` response[cite: 325].
    * Schema Definition (TypeScript Interface): [cite: 325]
    ```typescript
    interface GetRecommendationsResponseDto {
      recommendations: RecommendationItemDto[];
      totalPending: number;
      limit: number;
      offset: number;
    }
    ```
* **RecommendationActionRequestDto**
    * Description: DTO for `POST /api/v1/reviews/recommendations/{recommendation_id}/action` request[cite: 326].
    * Schema Definition (TypeScript Interface): [cite: 327]
    ```typescript
    type RecommendationAction = "approve_for_addition" | "reject";
    interface RecommendationActionRequestDto {
      action: RecommendationAction;
    }
    ```
* **ActionResponseDto**
    * Description: Generic DTO for action responses[cite: 328].
    * Schema Definition (TypeScript Interface): [cite: 328]
    ```typescript
    interface ActionResponseDto {
      id: string; // ID of the item actioned
      status: string; // New status
      message: string;
    }
    ```
* **RemovalSuggestionItemDto**
    * Description: Single removal suggestion item[cite: 329].
    * Schema Definition (TypeScript Interface): [cite: 330]
    ```typescript
    interface RemovalSuggestionItemDto {
      id: string; // Internal DB ID
      spotifyTrackId: string;
      trackName: string;
      artists: ArtistSummaryDto[];
      albumName: string;
      previewUrl?: string | null;
      spotifyTrackUrl: string;
      justification: string;
      reason: string; // e.g., "vibe_outlier", "release_age_exceeded"
      deviationScore?: number | null;
      suggestedAt: string; // ISO 8601
    }
    ```
* **GetRemovalSuggestionsResponseDto**
    * Description: DTO for `GET /api/v1/reviews/removals` response[cite: 332].
    * Schema Definition (TypeScript Interface): [cite: 332]
    ```typescript
    interface GetRemovalSuggestionsResponseDto {
      removals: RemovalSuggestionItemDto[];
      totalPending: number;
      limit: number;
      offset: number;
    }
    ```
* **RemovalActionRequestDto**
    * Description: DTO for `POST /api/v1/reviews/removals/{removal_id}/action` request[cite: 333].
    * Schema Definition (TypeScript Interface): [cite: 334]
    ```typescript
    type RemovalAction = "approve_removal" | "keep_in_playlist";
    interface RemovalActionRequestDto {
      action: RemovalAction;
    }
    ```

### 2.5. Playlist Modification & Debugging DTOs
* **ApplyChangesResponseDto**
    * Description: DTO for `POST /api/v1/playlists/managed/apply-changes` response[cite: 335].
    * Schema Definition (TypeScript Interface): [cite: 336]
    ```typescript
    interface ApplyChangesResponseDto {
      applyId: string; // Unique ID for the "apply changes" task
      message: string;
    }
    ```
* **ApplyChangesErrorDetailDto**
    * Description: Specific error during "apply changes"[cite: 337].
    * Schema Definition (TypeScript Interface): [cite: 337]
    ```typescript
    interface ApplyChangesErrorDetailDto {
      spotifyTrackId: string;
      action: "add" | "remove";
      error: string; // Description of why it failed
    }
    ```
* **ApplyChangesSummaryDto**
    * Description: Summary of "apply changes" results[cite: 338].
    * Schema Definition (TypeScript Interface): [cite: 339]
    ```typescript
    interface ApplyChangesSummaryDto {
      songsRequestedForAddition: number;
      songsSuccessfullyAdded: number;
      songsRequestedForRemoval: number;
      songsSuccessfullyRemoved: number;
      individualErrors?: ApplyChangesErrorDetailDto[];
    }
    ```
* **ApplyChangesStatusResponseDto**
    * Description: DTO for `GET /api/v1/playlists/managed/apply-changes/status/{apply_id}` response[cite: 340].
    * Schema Definition (TypeScript Interface): [cite: 340]
    ```typescript
    interface ApplyChangesStatusResponseDto {
      applyId: string;
      status: string; // e.g., "Pending", "Running_Additions", "Completed_Success"
      startedAt?: string | null; // ISO 8601
      completedAt?: string | null; // ISO 8601
      summary?: ApplyChangesSummaryDto | null;
    }
    ```
* **DebugErrorItemDto**
    * Description: Single error item in debug log[cite: 342].
    * Schema Definition (TypeScript Interface): [cite: 342]
    ```typescript
    interface DebugErrorItemDto {
      timestamp: string; // ISO 8601
      message: string;
      context?: object | string | null;
    }
    ```
* **GetDebugErrorsResponseDto**
    * Description: DTO for `GET /api/v1/debug/errors` response[cite: 343].
    * Schema Definition (TypeScript Interface): [cite: 344]
    ```typescript
    interface GetDebugErrorsResponseDto {
      errors: DebugErrorItemDto[];
    }
    ```
* **SyncLogItemDto (Revised)**
    * Description: Single sync log entry[cite: 345].
    * Schema Definition (TypeScript Interface): [cite: 345]
    ```typescript
    interface SyncLogItemDto {
      syncId: string;
      startedAt: string; // ISO 8601
      completedAt?: string | null; // ISO 8601
      status: string; // e.g., "Completed_Success", "Failed"
      durationMs?: number | null;
      playlistSnapshotIdBefore?: string | null;
      playlistSnapshotIdAfter?: string | null;
      dnaRecalculated?: boolean;
      recommendationsGenerated: number;
      removalsSuggested: number;
      llmCallsMadeCount?: number;
      totalLlmTokensUsed?: number | null;
      errorSummary?: string | null; // Brief summary if sync failed
    }
    ```
* **GetSyncLogResponseDto**
    * Description: DTO for `GET /api/v1/debug/sync-log` response[cite: 348].
    * Schema Definition (TypeScript Interface): [cite: 348]
    ```typescript
    interface GetSyncLogResponseDto {
      syncLog: SyncLogItemDto[];
    }
    ```

## 3. Database Schemas
This subsection details the PostgreSQL database table schemas[cite: 349]. A common `trigger_set_timestamp()` function is assumed to be created once and used by all tables for `updated_at` fields[cite: 350].

```sql
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 3.1. users
* **Purpose:** To store user Spotify identity and authentication tokens[cite: 352].
* **Schema Definition (PostgreSQL):** [cite: 353]
    ```sql
    CREATE TABLE users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        spotify_user_id VARCHAR(255) UNIQUE NOT NULL,
        spotify_display_name VARCHAR(255),
        spotify_access_token TEXT NOT NULL, -- Should be encrypted
        spotify_refresh_token TEXT, -- Should be encrypted
        spotify_token_expires_at TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    CREATE INDEX IF NOT EXISTS idx_users_spotify_user_id ON users(spotify_user_id);
    CREATE TRIGGER set_timestamp_users BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
    ```
* **Notes:** `id` is internal UUID. Tokens must be stored encrypted[cite: 355].

### 3.2. managed_playlists
* **Purpose:** To store details of the Spotify playlist actively managed by the agent for a user[cite: 356].
* **Schema Definition (PostgreSQL):** [cite: 357]
    ```sql
    CREATE TABLE managed_playlists (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        spotify_playlist_id VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        owner_display_name VARCHAR(255),
        track_count INTEGER NOT NULL DEFAULT 0,
        spotify_snapshot_id VARCHAR(255),
        last_synced_at TIMESTAMPTZ,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    CREATE INDEX IF NOT EXISTS idx_managed_playlists_user_id ON managed_playlists(user_id);
    CREATE INDEX IF NOT EXISTS idx_managed_playlists_spotify_playlist_id ON managed_playlists(spotify_playlist_id);
    CREATE TRIGGER set_timestamp_managed_playlists BEFORE UPDATE ON managed_playlists FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
    ```
* **Notes:** `user_id` UNIQUE enforces one managed playlist per user for MVP[cite: 360].

### 3.3. playlist_dna
* **Purpose:** To store the calculated "Playlist DNA" for a managed playlist[cite: 361].
* **Schema Definition (PostgreSQL with pgvector extension):** [cite: 362]
    ```sql
    CREATE EXTENSION IF NOT EXISTS vector; -- Ensure pgvector is enabled

    CREATE TABLE playlist_dna (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        managed_playlist_id UUID NOT NULL REFERENCES managed_playlists(id) ON DELETE CASCADE,
        spotify_snapshot_id VARCHAR(255) NOT NULL,
        dna_summary JSONB, -- Stores human-readable components like top artists, genres, key audio features
        dna_vector VECTOR(10), -- Example dimension, actual dimension to be determined by feature set
        calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    CREATE INDEX IF NOT EXISTS idx_playlist_dna_managed_playlist_id ON playlist_dna(managed_playlist_id);
    CREATE INDEX IF NOT EXISTS idx_playlist_dna_snapshot_id ON playlist_dna(spotify_snapshot_id);
    -- CREATE INDEX IF NOT EXISTS idx_playlist_dna_vector ON playlist_dna USING hnsw (dna_vector vector_l2_ops); -- Example for pgvector index, if direct DB similarity search is needed later
    CREATE TRIGGER set_timestamp_playlist_dna BEFORE UPDATE ON playlist_dna FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
    ```
* **Notes:** `dna_summary` (JSONB) stores human-readable components. `dna_vector` (VECTOR) stores the numerical centroid (dimension e.g., 10)[cite: 365].

### 3.4. pending_recommendations
* **Purpose:** To store new song recommendations awaiting user review[cite: 366].
* **Schema Definition (PostgreSQL):** [cite: 367]
    ```sql
    CREATE TYPE recommendation_status AS ENUM ('pending_review', 'approved_for_addition', 'rejected', 'applied_to_spotify', 'failed_to_apply');

    CREATE TABLE pending_recommendations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        managed_playlist_id UUID NOT NULL REFERENCES managed_playlists(id) ON DELETE CASCADE,
        spotify_track_id VARCHAR(255) NOT NULL,
        track_name VARCHAR(255) NOT NULL,
        artists JSONB, -- Store as array of objects: [{"name": "Artist Name"}]
        album_name VARCHAR(255),
        album_release_date DATE,
        preview_url TEXT,
        spotify_track_url TEXT,
        justification TEXT,
        similarity_score REAL,
        status recommendation_status NOT NULL DEFAULT 'pending_review',
        discovered_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        actioned_at TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    CREATE INDEX IF NOT EXISTS idx_pending_recommendations_playlist_status ON pending_recommendations(managed_playlist_id, status, discovered_at DESC);
    CREATE INDEX IF NOT EXISTS idx_pending_recommendations_spotify_track_id ON pending_recommendations(spotify_track_id);
    CREATE UNIQUE INDEX IF NOT EXISTS uq_pending_recommendation_active_review ON pending_recommendations (managed_playlist_id, spotify_track_id) WHERE (status = 'pending_review');
    CREATE TRIGGER set_timestamp_pending_recommendations BEFORE UPDATE ON pending_recommendations FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
    ```
* **Notes:** `status` uses ENUM. Unique index prevents duplicate active pending reviews[cite: 370].

### 3.5. pending_removal_suggestions
* **Purpose:** To store songs from the managed playlist suggested for removal[cite: 371].
* **Schema Definition (PostgreSQL):** [cite: 372]
    ```sql
    CREATE TYPE removal_suggestion_status AS ENUM ('pending_review', 'remove_approved', 'kept_by_user', 'applied_to_spotify', 'failed_to_apply');
    CREATE TYPE removal_suggestion_reason AS ENUM ('vibe_outlier', 'release_age_exceeded', 'other');

    CREATE TABLE pending_removal_suggestions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        managed_playlist_id UUID NOT NULL REFERENCES managed_playlists(id) ON DELETE CASCADE,
        spotify_track_id VARCHAR(255) NOT NULL,
        track_name VARCHAR(255) NOT NULL,
        artists JSONB, -- Store as array of objects: [{"name": "Artist Name"}]
        album_name VARCHAR(255),
        preview_url TEXT,
        spotify_track_url TEXT,
        justification TEXT,
        reason_for_suggestion removal_suggestion_reason NOT NULL,
        deviation_score REAL,
        status removal_suggestion_status NOT NULL DEFAULT 'pending_review',
        suggested_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        actioned_at TIMESTAMPTZ,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    CREATE INDEX IF NOT EXISTS idx_pending_removals_playlist_status ON pending_removal_suggestions(managed_playlist_id, status, suggested_at DESC);
    CREATE INDEX IF NOT EXISTS idx_pending_removals_spotify_track_id ON pending_removal_suggestions(spotify_track_id);
    CREATE UNIQUE INDEX IF NOT EXISTS uq_pending_removal_active_review ON pending_removal_suggestions (managed_playlist_id, spotify_track_id) WHERE (status = 'pending_review');
    CREATE TRIGGER set_timestamp_pending_removal_suggestions BEFORE UPDATE ON pending_removal_suggestions FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
    ```
* **Notes:** Includes `reason_for_suggestion` ENUM. Unique index prevents duplicate active pending reviews[cite: 375].

### 3.6. sync_log_entries
* **Purpose:** To log each execution of the full synchronization process[cite: 376].
* **Schema Definition (PostgreSQL):** [cite: 377]
    ```sql
    CREATE TYPE sync_status AS ENUM ('initiated', 'dna_analysis_started', 'discovery_started', 'suggestion_generation_started', 'completed_success', 'completed_with_errors', 'failed');

    CREATE TABLE sync_log_entries (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        sync_id UUID UNIQUE NOT NULL, -- ID for the triggered sync task
        managed_playlist_id UUID NOT NULL REFERENCES managed_playlists(id) ON DELETE CASCADE,
        status sync_status NOT NULL DEFAULT 'initiated',
        started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        completed_at TIMESTAMPTZ,
        duration_ms INTEGER,
        playlist_snapshot_id_before VARCHAR(255),
        playlist_snapshot_id_after VARCHAR(255),
        dna_recalculated BOOLEAN,
        recommendations_generated_count INTEGER DEFAULT 0,
        removals_suggested_count INTEGER DEFAULT 0,
        llm_calls_made_count INTEGER DEFAULT 0,
        total_llm_tokens_used INTEGER,
        error_summary TEXT,
        details JSONB, -- For stage-specific metrics or detailed errors
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
    CREATE INDEX IF NOT EXISTS idx_sync_log_entries_managed_playlist_id ON sync_log_entries(managed_playlist_id, started_at DESC);
    CREATE INDEX IF NOT EXISTS idx_sync_log_entries_sync_id ON sync_log_entries(sync_id);
    CREATE INDEX IF NOT EXISTS idx_sync_log_entries_status ON sync_log_entries(status);
    CREATE TRIGGER set_timestamp_sync_log_entries BEFORE UPDATE ON sync_log_entries FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();
    ```
* **Notes:** `sync_id` is unique. `status` uses ENUM. Includes counts for LLM calls and tokens[cite: 381].

### 3.7. application_error_logs
* **Purpose:** To store critical error logs from the backend for debugging via the Debugging Screen[cite: 382].
* **Schema Definition (PostgreSQL):** [cite: 383]
    ```sql
    CREATE TYPE error_log_level AS ENUM ('ERROR', 'CRITICAL');

    CREATE TABLE application_error_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        log_level error_log_level NOT NULL,
        logger_name VARCHAR(255), -- e.g., module name
        message TEXT NOT NULL,
        context JSONB, -- For structured error details like stack trace, request ID
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW() -- No updated_at needed for logs
    );
    CREATE INDEX IF NOT EXISTS idx_application_error_logs_timestamp ON application_error_logs(timestamp DESC);
    CREATE INDEX IF NOT EXISTS idx_application_error_logs_log_level ON application_error_logs(log_level);
    ```
* **Notes:** For ERROR and CRITICAL logs[cite: 385]. `context` (JSONB) for structured details. Retention policy should be considered[cite: 386].