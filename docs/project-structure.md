# Project Structure

The project will adopt a **Monorepo** structure to house the backend, frontend, shared documentation, and scripts. This approach simplifies dependency management and cross-application consistency for a solo developer.

```plaintext
playlist-intelligence-agent/
├── .augmentignore              # Augment configuration
├── .env.example                # Root environment variables (for Docker Compose)
├── .gitignore                  # Git ignore rules for Python, Node, OS files
├── README.md                   # Root project overview and setup instructions
├── docker-compose.yml          # Docker Compose configuration for local development
├── backend/                    # Python FastAPI Backend Application
│   ├── .env.example            # Backend environment variables template
│   ├── README.md               # Backend specific README
│   ├── pyproject.toml          # Python project metadata & dependencies (uv)
│   ├── uv.lock                 # Locked dependency versions
│   ├── alembic.ini             # Alembic configuration
│   ├── alembic/                # Alembic database migration scripts
│   │   ├── env.py              # Alembic environment configuration
│   │   ├── script.py.mako      # Migration script template
│   │   └── versions/           # Database migration files
│   │       └── *.py            # Individual migration scripts
│   └── src/
│       └── app/                # Main application package
│           ├── __init__.py
│           ├── main.py         # FastAPI application entry point
│           ├── config.py       # Application configuration loading
│           ├── logging_config.py # Logging configuration
│           ├── api/            # API endpoint routers/controllers
│           │   ├── __init__.py
│           │   ├── auth.py     # Authentication endpoints
│           │   ├── dependencies.py # FastAPI dependencies
│           │   ├── health.py   # Health check endpoints
│           │   └── playlists_router.py # Playlist management endpoints
│           ├── core/           # Core application components
│           │   ├── __init__.py
│           │   ├── exceptions.py # Custom exception classes
│           │   ├── security.py # Security utilities
│           │   ├── security_config.py # Security configuration
│           │   └── models/     # Core domain models/schemas
│           │       ├── __init__.py
│           │       ├── base.py # Base model classes
│           │       ├── user.py # User models
│           │       ├── app_settings.py # Application settings models
│           │       └── dtos.py # Data transfer objects
│           ├── db/             # Database configuration
│           │   ├── __init__.py
│           │   └── session.py  # Database session management
│           ├── integrations/   # External service integrations
│           │   ├── __init__.py
│           │   └── spotify_service.py # Spotify API client
│           └── repositories/   # Data access layer
│               ├── __init__.py
│               └── user_repository.py # User data access
├── frontend/                   # Vue.js Frontend Application
│   ├── index.html              # Main HTML file
│   ├── package.json            # Frontend project manifest & dependencies
│   ├── vite.config.ts          # Vite configuration
│   └── src/                    # Frontend source code
│       ├── main.ts             # Frontend application entry point
│       └── App.vue             # Root Vue component
├── docs/                       # Project documentation
│   ├── index.md                # Main documentation index
│   ├── operational-guidelines.md # Development guidelines
│   ├── tech-stack.md           # Technology stack documentation
│   ├── project-structure.md    # This file
│   ├── environment-vars.md     # Environment variables documentation
│   ├── key-references.md       # Important references and links
│   └── ...                     # Other documentation files
└── scripts/                    # Utility scripts
    └── README.md               # Scripts documentation
```

## Key Directory Descriptions:

### Root Level
* `.augmentignore`: Configuration file for Augment to exclude certain directories from indexing
* `.env.example`: Template for root-level environment variables (primarily for Docker Compose)
* `.gitignore`: Git ignore rules for Python, Node.js, OS files, and sensitive data
* `docker-compose.yml`: Docker Compose configuration for local development database setup
* `README.md`: Root project overview and setup instructions

### Backend (`backend/`)
Houses the entire Python FastAPI backend application:
* `.env.example`: Backend-specific environment variables template
* `pyproject.toml`: Python project metadata and dependencies managed by `uv`
* `uv.lock`: Locked dependency versions for reproducible builds
* `alembic.ini`: Alembic database migration configuration
* `alembic/`: Database migration scripts and configuration
* `src/app/`: Main application package containing:
    * `main.py`: FastAPI application entry point with middleware and router configuration
    * `config.py`: Application configuration loading from environment variables
    * `logging_config.py`: Structured JSON logging configuration
    * `api/`: FastAPI routers and endpoint definitions
    * `core/`: Core application components (exceptions, security, models)
    * `db/`: Database session management and configuration
    * `integrations/`: External service clients (Spotify API)
    * `repositories/`: Data access layer abstracting database operations

### Frontend (`frontend/`)
Houses the Vue.js frontend application:
* `package.json`: Frontend dependencies and build scripts
* `vite.config.ts`: Vite build tool configuration
* `index.html`: Main HTML template
* `src/`: Frontend source code with Vue 3 + TypeScript

### Documentation (`docs/`)
Contains comprehensive project documentation:
* `index.md`: Main documentation index
* `operational-guidelines.md`: Development workflow and coding standards
* `tech-stack.md`: Technology choices and versions
* `project-structure.md`: This file describing the project organization
* `environment-vars.md`: Environment variable documentation
* `key-references.md`: Important external references and links

### Scripts (`scripts/`)
Utility scripts for development, deployment, and operational tasks.

## Notes:
This Monorepo structure is designed to support independent development and testing of the backend and frontend applications while facilitating shared documentation and scripts. The `architecture-tmpl` also includes `infra/` for IaC and `build/` for compiled output, which can be added if/when those become relevant. For the current MVP focus, they are omitted but can be easily incorporated.