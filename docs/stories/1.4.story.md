# Story 1.4: Core Spotify Authentication & Token Management

## Status: Complete

## Story

- As a User (the primary system user), I want to securely authenticate the application with my Spotify account using OAuth 2.0 (requesting all necessary playlist read/write scopes), so that the application can access my playlist data and make changes on my behalf, with tokens being securely managed by the backend.

## Acceptance Criteria (ACs)

1.  The backend API (FastAPI application from Story 1.2) `shall` provide endpoints to initiate the Spotify OAuth 2.0 Authorization Code Flow:
    * An endpoint (e.g., `GET /api/v1/auth/login`) that redirects the user to Spotify's authorization page.
    * An endpoint (e.g., `GET /api/v1/auth/callback`) for Spotify to redirect back to after user authorization.
2.  During the authentication initiation, the application `shall` correctly request the necessary Spotify API scopes: `playlist-read-private`, `playlist-read-collaborative`, `playlist-modify-public`, and `playlist-modify-private`.
3.  Upon successful user authorization and callback from Spotify, the backend `shall` securely exchange the authorization code for Spotify access tokens (access token and refresh token) using the configured Spotify Client ID and Client Secret.
4.  The received Spotify access token and refresh token `shall` be **encrypted** and then securely stored by the backend system in the `users` table of the PostgreSQL database (setup in Story 1.3), associated with the user's `spotify_user_id` and `spotify_display_name`. The token's expiry time `shall` also be stored.
5.  The backend `shall` implement a mechanism (e.g., within a `SpotifyIntegrationService`) to use the stored (decrypted) refresh token to obtain a new access token from Spotify when the current access token is expired or nearing expiration. The newly refreshed token and its expiry `shall` be updated (encrypted) in the database.
6.  The placeholder frontend application (from Story 1.1) `shall` include a basic UI element (e.g., a "Connect to Spotify" button or link on a conceptual login/landing page) that, when activated, directs the user to the backend's `/api/v1/auth/login` endpoint.
7.  Upon successful completion of the authentication flow at the `/api/v1/auth/callback` endpoint, the backend `shall` issue an application-specific JWT (as per Main Architecture Document Sec 5.2), set it in a secure, HttpOnly cookie, and then redirect the user to a designated frontend route (e.g., `/dashboard` or `/select-playlist` as per `docs/api-reference.md`).
8.  The backend `shall` log key events during the auth flow (e.g., auth initiation, callback received, token exchange success/failure) for debugging purposes.

## Tasks / Subtasks

- [X] **Task 1: Configure Spotify App Credentials** (AC: 3, 8)
    - [X] Add `SPOTIFY_CLIENT_ID`, `SPOTIFY_CLIENT_SECRET`, `SPOTIFY_REDIRECT_URI` to `backend/src/app/config.py` (loaded from environment variables).
    - [X] Update `backend/.env.example` with these variables.
    - [X] Developer needs to create a Spotify Developer App and populate these in their local `backend/.env`.
- [X] **Task 2: Add Dependencies**
    - [X] Using `uv pip install`, add `spotipy` (Spotify API client), `python-jose[cryptography]` (for JWT creation/validation), and `cryptography` (for encrypting tokens in DB) to `backend/pyproject.toml`.
- [X] **Task 3: Implement Token Encryption/Decryption Service** (AC: 4, 5)
    - [X] Create a utility/service (e.g., `app/core/security.py`) with functions to encrypt and decrypt text data using a symmetric encryption key (e.g., Fernet from `cryptography`).
    - [X] The encryption key (`TOKENS_ENCRYPTION_KEY`) MUST be loaded from environment variables via `config.py`.
- [X] **Task 4: Implement User Data Service/Repository** (AC: 4)
    - [X] Create SQLAlchemy model for the `users` table (as defined in `docs/data-models.md`). Update/create Alembic migration if placeholder from Story 1.3 needs adjustment.
    - [X] Implement functions to create/update user details (including encrypted tokens and expiry) and retrieve user by `spotify_user_id`.
- [X] **Task 5: Implement `/api/v1/auth/login` Endpoint** (AC: 1, 2)
    - [X] Create a FastAPI router for auth.
    - [X] Implement the `GET /auth/login` endpoint.
    - [X] Use `spotipy.SpotifyOAuth` with configured credentials, redirect URI, and required scopes (`playlist-read-private`, `playlist-read-collaborative`, `playlist-modify-public`, `playlist-modify-private`).
    - [X] Redirect the user to the URL obtained from `sp_oauth.get_authorize_url()`.
- [X] **Task 6: Implement `/api/v1/auth/callback` Endpoint** (AC: 1, 3, 4, 7, 8)
    - [X] Implement the `GET /auth/callback` endpoint.
    - [X] Retrieve the `code` query parameter.
    - [X] Use `sp_oauth.get_access_token(code, check_cache=False)` to exchange the code for Spotify tokens.
    - [X] Fetch user profile info (e.g., `spotify_user_id`, `display_name`) using the obtained access token via Spotipy.
    - [X] Call User Data Service to save/update user info, including encrypted Spotify tokens and expiry.
    - [X] Create an application-specific JWT containing user identifiers (e.g., internal `user_id`, `spotify_user_id`).
    - [X] Set this JWT in an HttpOnly, Secure, SameSite cookie.
    - [X] Redirect the user to the frontend (e.g., `settings.FRONTEND_URL + "/dashboard"` or `settings.FRONTEND_URL + "/select-playlist"` - add `FRONTEND_URL` to `config.py`).
    - [X] Implement robust error handling (e.g., if `code` is invalid or token exchange fails).
- [X] **Task 7: Implement Spotify Token Refresh Logic** (AC: 5)
    - [X] Create a function within `SpotifyIntegrationService` (or similar) that:
        - [X] Retrieves current (encrypted) tokens and expiry for a user. Decrypts them.
        - [X] Checks if the access token is expired or nearing expiry (e.g., within next 5-10 minutes).
        - [X] If expired, uses `spotipy.SpotifyOAuth` and the refresh token to get a new access token.
        - [X] Encrypts and updates the new access token and its new expiry time in the database for the user.
    - [X] Ensure services that use Spotify tokens call this refresh logic before making API calls.
- [X] **Task 8: Basic Frontend Integration** (AC: 6)
    - [X] In the placeholder `frontend/` app, add a simple link or button that navigates to `/api/v1/auth/login`.
- [X] **Task 9: Update Backend README** (AC: 1, 8)
    - [X] Add instructions for setting up Spotify Developer App credentials (`SPOTIFY_CLIENT_ID`, `SPOTIFY_CLIENT_SECRET`, `SPOTIFY_REDIRECT_URI`) and `TOKENS_ENCRYPTION_KEY` / `JWT_SECRET_KEY` in `.env`.
- [X] **Task 10: Verification**
    - [X] Manually test the full OAuth flow: click frontend link, authorize on Spotify, get redirected back to frontend.
    - [X] Check database to ensure user is created and tokens are stored (and appear encrypted).
    - [X] Verify application JWT cookie is set.
    - [X] Check logs for auth events.
    - [X] Test token refresh logic (may require manual token expiry modification or waiting).

## Dev Technical Guidance

* **Spotify API Client:** Use `spotipy` library. Refer to `docs/tech-stack.md`.
    * `SpotifyOAuth(client_id, client_secret, redirect_uri, scope=scopes_string, cache_path=None)`
    * Scopes string: `"playlist-read-private playlist-read-collaborative playlist-modify-public playlist-modify-private"`
* **Token Encryption:** Use `cryptography.fernet.Fernet`. Key (`TOKENS_ENCRYPTION_KEY`) must be 32 url-safe base64-encoded bytes. Generate one using `Fernet.generate_key()` and store it as an environment variable. See `docs/operational-guidelines.md` (Security Best Practices) and `docs/environment-vars.md`.
* **Application JWTs:** Use `python-jose` library for creating and potentially verifying JWTs.
    * Define `JWT_SECRET_KEY` (strong random string) and algorithm (e.g., `HS256`) in `config.py` from env vars.
    * JWT payload should include `sub` (e.g., internal user ID or `spotify_user_id`) and `exp` (expiry time).
    * Set cookie attributes: `HttpOnly=True`, `Secure=True` (in production), `SameSite='Lax'` or `'Strict'`.
* **Database:** The `users` table schema is defined in `docs/data-models.md`. Use SQLAlchemy models and session from Story 1.3.
* **Configuration (`config.py`):** Add `SPOTIFY_CLIENT_ID`, `SPOTIFY_CLIENT_SECRET`, `SPOTIFY_REDIRECT_URI`, `TOKENS_ENCRYPTION_KEY`, `JWT_SECRET_KEY`, `JWT_ALGORITHM`, `JWT_EXPIRY_MINUTES`, `FRONTEND_URL`.
* **Redirect URI:** Must be exactly what is configured in your Spotify Developer Dashboard for the app. For local dev, typically `http://localhost:8000/api/v1/auth/callback` (assuming backend runs on port 8000).
* **Error Handling:** Implement try-except blocks for Spotipy calls and token storage. Redirect to an error page on frontend or return appropriate error responses if callback fails.
* **Frontend Link Example (conceptual):**
    ```html
    <a :href="`${backendApiUrl}/api/v1/auth/login`">Connect to Spotify</a> 
    ```
    (Where `backendApiUrl` is configured, or just `/api/v1/auth/login` if served on same domain initially, though unlikely with FE on Vercel/Netlify and BE on Koyeb). For MVP placeholder, a simple `/api/v1/auth/login` link is fine.

## Story Progress Notes

### Agent Model Used: `Claude Sonnet 4 (Augment Agent)`

### Completion Notes List
* Successfully implemented complete Spotify OAuth 2.0 Authorization Code Flow with all required scopes
* Implemented robust Fernet encryption for secure token storage in database with proper error handling
* Created comprehensive user management system with SQLAlchemy models and repository pattern
* Implemented secure JWT creation and validation with proper expiration and algorithm configuration
* Added production-ready cookie security with HttpOnly, Secure, and SameSite attributes
* Integrated Spotipy client with automatic token refresh and error handling
* Updated frontend with styled Spotify login integration
* Implemented comprehensive logging for authentication events and security monitoring
* Added proper exception handling for all authentication failure scenarios
* Created secure configuration management for all authentication-related environment variables

### Change Log
* Updated `backend/pyproject.toml` - Added spotipy 2.24.0, python-jose[cryptography], and cryptography dependencies
* Updated `backend/.env.example` - Added Spotify OAuth credentials and encryption keys
* Updated `backend/src/app/config.py` - Added comprehensive authentication configuration with validation
* Created `backend/src/app/core/security.py` - Fernet encryption/decryption and JWT creation/verification with proper error handling
* Created `backend/src/app/core/models/user.py` - SQLAlchemy User model with encrypted token fields
* Created `backend/src/app/repositories/user_repository.py` - User data access layer with create/update operations
* Created `backend/alembic/versions/d8ae84a76bdd_initial_migration_with_users_and_app_.py` - Database migration with users table
* Created `backend/src/app/integrations/spotify_service.py` - Spotify OAuth client and service integration
* Created `backend/src/app/api/auth.py` - Complete OAuth endpoints with login and callback handling
* Updated `backend/src/app/main.py` - Integrated auth router and middleware
* Updated `frontend/src/App.vue` - Added styled "Connect to Spotify" button with proper backend integration
* Updated `backend/README.md` - Comprehensive authentication setup and configuration documentation