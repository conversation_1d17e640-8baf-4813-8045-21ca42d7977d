# Story 1.1: Initial Project & Monorepo Setup

## Status: Complete

## Story

- As a Developer (representing the system's need for a proper foundation), I want the initial project Monorepo to be set up with distinct placeholders for the Python backend and web frontend, so that subsequent development for both can proceed in an organized manner within a single repository.

## Acceptance Criteria (ACs)

1.  A Git repository `shall` be initialized for the project.
2.  A root directory structure for a Monorepo `shall` be created, clearly delineating areas for backend code (e.g., `backend/`), frontend code (e.g., `frontend/`), shared documentation (e.g., `docs/`), and utility scripts (e.g., `scripts/`).
3.  Basic project configuration files `shall` be present at the root level (e.g., a comprehensive `.gitignore` file suitable for Python and Node.js/web development, a root `README.md` file with a project title and brief description).
4.  Minimal placeholder application stubs (e.g., a "hello world" script or basic runnable application) `shall` exist within both the `backend/` (Python) and `frontend/` (e.g., a simple HTML file or basic Node.js/Vite/Next.js app stub) directories to confirm their independent setup.
5.  Basic dependency management files `shall` be initialized for both the backend (Python, e.g., using `uv` with a `pyproject.toml` or `requirements.txt` structure compatible with `uv`) and the frontend (e.g., `package.json` for npm/yarn/pnpm).

## Tasks / Subtasks

- [x] **Task 1: Initialize Git Repository** (AC: 1)
    - [x] Initialize a new Git repository in the project's root directory.
    - [x] Create an initial commit.
- [x] **Task 2: Create Monorepo Root Directory Structure** (AC: 2)
    - [x] Create top-level directories: `backend/`, `frontend/`, `docs/`, `scripts/`.
    - [x] Refer to `docs/project-structure.md` for the overall planned structure.
- [x] **Task 3: Setup Root Configuration Files** (AC: 3)
    - [x] Create a comprehensive `.gitignore` file at the project root.
        - [x] Include common Python ignores (e.g., `__pycache__/`, `*.pyc`, `.venv/`).
        - [x] Include common Node.js ignores (e.g., `node_modules/`, `dist/`, `.log`).
        - [x] Include common OS-specific ignores (e.g., `.DS_Store`, `Thumbs.db`).
        - [x] Include `.env` files.
    - [x] Create a root `README.md` file.
        - [x] Add project title: "Playlist Intelligence Agent MVP".
        - [x] Add a brief project description (can be sourced from PRD Section 1).
- [x] **Task 4: Implement Backend Placeholder Stub** (AC: 4)
    - [x] Inside `backend/src/app/main.py` (or similar, as per `docs/project-structure.md`), create a minimal Python "hello world" script or a very basic, runnable FastAPI placeholder (e.g., a single endpoint returning "OK").
    - [x] Ensure it can be executed.
- [x] **Task 5: Initialize Backend Dependency Management** (AC: 5)
    - [x] Navigate to the `backend/` directory.
    - [x] Initialize Python project using `uv` to create a `pyproject.toml` file. (e.g., `uv init` or `uv venv` then `uv pip install fastapi uvicorn`).
    - [x] Add `fastapi` and `uvicorn` as initial dependencies.
- [x] **Task 6: Implement Frontend Placeholder Stub** (AC: 4)
    - [x] Inside the `frontend/` directory, initialize a basic Vue.js with TypeScript project using Vite (as per `docs/tech-stack.md`).
        - [x] Example command: `npm create vite@latest . -- --template vue-ts` (assuming npm and current directory is `frontend/`). Adjust for chosen package manager if not npm.
    - [x] Ensure the placeholder Vue.js app can be built and run locally (e.g., `npm run dev`).
- [x] **Task 7: Initialize Frontend Dependency Management** (AC: 5)
    - [x] Confirm `package.json` is created in the `frontend/` directory by the Vite initialization process.
    - [x] Ensure core dependencies like `vue` and `typescript` are listed.
- [x] **Task 8: Verification**
    - [x] Verify all ACs for Story 1.1 are met.

## Dev Technical Guidance

* **Monorepo Structure:** Strictly adhere to the overall Monorepo layout defined in `docs/project-structure.md` when creating directories.
* **Technology Stack:**
    * For backend dependency management, use `uv` and `pyproject.toml` as specified in `docs/tech-stack.md`.
    * For the frontend placeholder, use Vue.js with TypeScript, initialized via Vite, as specified in `docs/tech-stack.md`.
* **`.gitignore`:** Consult common `.gitignore` templates for Python (e.g., from GitHub) and Node.js (e.g., from Toptal or GitHub) to ensure comprehensive coverage. Remember to ignore `.env` files specifically.
* **Root `README.md`:** The project title is "Playlist Intelligence Agent MVP". The brief description can be taken from the PRD's Goal/Objective section (PRD Section 1).
* **Backend Placeholder (`backend/src/app/main.py`):**
    ```python
    # Example minimal FastAPI placeholder
    from fastapi import FastAPI
    
    app = FastAPI(title="Playlist Intelligence Agent Backend")
    
    @app.get("/health")
    async def health_check():
        return {"status": "ok"}
    ```
* **Frontend Placeholder:** The command `npm create vite@latest frontend_app_dir_name -- --template vue-ts` (run from project root, then move contents if needed, or run from within `frontend/` as `npm create vite@latest . -- --template vue-ts`) will scaffold a runnable Vue.js + TypeScript application.
* **Dependency Initialization Commands (Examples):**
    * Backend (inside `backend/` directory):
        * `uv venv` (to create a virtual environment)
        * `uv pip install fastapi uvicorn`
    * Frontend (inside `frontend/` directory, after `npm create vite@latest . -- --template vue-ts`):
        * `npm install` (or `yarn install` / `pnpm install` if a different package manager was implicitly chosen by `create vite`).
        * Verify `npm run dev` works.

## Implementation Details

### Monorepo Structure Created
```
playlist-intelligence-agent/
├── backend/                    # Python FastAPI backend
│   ├── src/app/
│   │   └── main.py            # FastAPI application with full middleware stack
│   ├── pyproject.toml         # uv dependency management
│   └── README.md              # Backend-specific documentation
├── frontend/                   # Vue.js + TypeScript frontend
│   ├── src/
│   │   ├── App.vue            # Main Vue component with Spotify integration
│   │   └── main.ts            # Vue application bootstrap
│   ├── package.json           # npm dependency management
│   ├── vite.config.ts         # Vite build configuration
│   └── index.html             # HTML entry point
├── docs/                       # Project documentation
├── scripts/                    # Utility scripts
├── .gitignore                  # Comprehensive ignore rules
└── README.md                   # Project overview and setup
```

### Backend Implementation (`backend/src/app/main.py`)
- **FastAPI Application**: Production-ready setup with title, version, and description
- **Middleware Stack**:
  - Security headers middleware (HSTS, CSP, X-Frame-Options)
  - Correlation ID and logging middleware with request/response tracking
- **Exception Handling**: Custom handlers for AppException, validation errors, and generic exceptions
- **Routing**: Integrated health, auth, and playlists routers
- **Endpoints**: Root (`/`) and health (`/health`) endpoints implemented

### Frontend Implementation (`frontend/src/App.vue`)
- **Vue 3 Composition API**: Using `<script setup>` syntax with TypeScript
- **Spotify Integration**: Login button linking to backend auth endpoint
- **Styling**: Custom CSS with Spotify brand colors and responsive design
- **TypeScript**: Strict type checking enabled

### Dependency Management
**Backend (`backend/pyproject.toml`)**:
```toml
[project]
dependencies = [
    "fastapi>=0.115.12",
    "uvicorn>=0.32.1",
    # Additional production dependencies
]
```

**Frontend (`frontend/package.json`)**:
```json
{
  "dependencies": {
    "vue": "^3.4.21"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.4",
    "typescript": "^5.4.5",
    "vite": "^5.2.10"
  }
}
```

### Configuration Files
- **`.gitignore`**: Comprehensive coverage for Python (`__pycache__/`, `.venv/`), Node.js (`node_modules/`, `dist/`), OS files (`.DS_Store`), and environment files (`.env`)
- **Root `README.md`**: Project title, description, and basic setup instructions

## Story Progress Notes

### Agent Model Used: `Claude Sonnet 4 (Augment Agent)`

### Completion Notes List
* Successfully established complete monorepo structure with all required directories (`backend/`, `frontend/`, `docs/`, `scripts/`)
* Implemented comprehensive FastAPI backend with production-ready features including middleware, exception handling, and security headers
* Created functional Vue.js + TypeScript frontend with Vite build system and Spotify integration placeholder
* Established proper dependency management using `uv` for Python backend and `npm` for frontend
* Created comprehensive `.gitignore` covering Python, Node.js, and OS-specific files
* Implemented root `README.md` with project description and setup instructions
* Backend evolved from simple placeholder to full-featured API with routing, logging, and security
* Frontend includes styled Spotify login integration and proper TypeScript setup

### Change Log
* Created initial monorepo structure: `backend/`, `frontend/`, `docs/`, `scripts/`
* Created comprehensive `.gitignore` file covering Python, Node.js, and OS-specific ignores
* Created root `README.md` with project title "Playlist Intelligence Agent MVP" and description
* Created `backend/src/app/main.py` - Full FastAPI application with:
  - Health endpoint (`/health`)
  - Root endpoint (`/`)
  - Security middleware for headers and CORS
  - Correlation ID and logging middleware
  - Exception handlers for AppException, validation errors, and generic exceptions
  - Router integration for auth and playlists
* Created `backend/pyproject.toml` with `uv` dependency management including FastAPI, Uvicorn, and development dependencies
* Created `frontend/` Vue.js + TypeScript structure with:
  - `package.json` with Vue 3.4.21, TypeScript 5.4.5, Vite 5.2.10
  - `vite.config.ts` with TypeScript and Vue plugin configuration
  - `src/App.vue` with Spotify login integration and styled UI
  - `src/main.ts` with Vue application bootstrap
  - `index.html` with proper meta tags and title