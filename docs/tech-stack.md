# Definitive Tech Stack Selections

This section outlines the definitive technology choices for the Playlist Intelligence Agent MVP. These selections are based on the project requirements (PRD), research findings, and prior discussions. This table is the **single source of truth** for all technology selections. Pinning specific versions is crucial for consistency and to avoid unexpected breaking changes, especially for AI-driven development.

| Category             | Technology              | Version / Details           | Description / Purpose                                   | Justification                                                                |
| :------------------- | :---------------------- | :-------------------------- | :------------------------------------------------------ | :-------------------------------------------------------------------------------------- |
| **Languages** | Python                  | 3.12.0+                     | Primary language for backend logic and API development. | Chosen for its strong data science ecosystem, robust web frameworks (FastAPI), and developer familiarity. |
|                      | TypeScript              | 5.4.5                       | Primary language for frontend development.              | For type safety and improved maintainability in the Vue.js frontend.      |
| **Runtime** | Node.js                 | 20.12.2 LTS                 | JavaScript runtime for the frontend build process (Vite). | Standard for modern JavaScript frontend development.                   |
| **Frameworks** | FastAPI                 | 0.115.12                    | Backend API framework (Python).                         | Modern, high-performance, with built-in data validation and async support.            |
|                      | Vue.js                  | 3.4.21                      | Frontend JavaScript framework.                          | Progressive, performant, and considered easier to learn for building interactive UIs. |
|                      | Vite                    | 5.2.10                      | Frontend build tool and development server.             | Fast build times and optimized development experience for Vue.js.        |
| **Databases** | PostgreSQL              | 16+                         | Primary relational data store.                          | Robust, open-source, supports JSONB and complex queries.                   |
|                      | pgvector                | (Compatible with PG 16+)    | PostgreSQL extension for vector similarity search.      | To store and query "Playlist DNA" vectors for similarity tasks.        |
| **Hosting Strategy** | Docker                  | Latest Stable               | Containerization for development and deployment.        | Ensures consistent environments and simplifies deployment.                                       |
|                      | Docker Compose          | Latest Stable               | Tool for defining and running multi-container Docker apps. | Simplifies local development setup of backend, DB, etc.               |
| **Python Libraries** | Spotipy                 | 2.24.0                      | Spotify Web API client for Python.                      | Simplifies Spotify API interactions.                                   |
|                      | SQLAlchemy              | 2.0.36                      | SQL toolkit and Object Relational Mapper (ORM).         | For database interaction in Python.                                  |
|                      | Alembic                 | 1.14.0                      | Database migration tool for SQLAlchemy.                 | Manages database schema changes.                                     |
|                      | Pydantic                | 2.10.4                      | Data validation and settings management.                | Used by FastAPI for request/response validation.                         |
|                      | Pydantic Settings       | 2.7.1                       | Settings management using Pydantic.                     | For loading configuration from environment variables.                    |
|                      | Uvicorn                 | 0.32.1                      | ASGI server for FastAPI.                                | To run the FastAPI application.                                      |
|                      | AsyncPG                 | 0.30.0                      | Async PostgreSQL driver for Python.                     | For asynchronous database operations.                                 |
|                      | psycopg2-binary         | 2.9.10                      | PostgreSQL adapter for Python (sync operations).        | For synchronous database operations (Alembic migrations).             |
|                      | python-jose             | 3.3.0                       | JWT library for Python.                                 | For JWT token creation and validation.                                |
|                      | cryptography            | 44.0.0                      | Cryptographic library for Python.                       | For encrypting sensitive data like Spotify tokens.                    |
|                      | python-json-logger      | 2.0.7                       | JSON formatter for Python logging.                      | For structured logging output.                                        |
|                      | Ruff                    | 0.11.12+                    | Fast Python linter and formatter.                       | For code formatting and linting.                                     |
|                      | uv                      | Latest Stable               | Python packaging tool (alternative to pip/venv).        | For efficient Python dependency management.                           |
| **Frontend Libraries**| Native Fetch API        | Browser-provided            | For HTTP requests from the frontend.                    | Built-in, avoids adding extra libraries for simple API calls.             |
| **Testing** | Pytest                  | Latest Stable               | Testing framework for Python.                           | For backend unit and integration tests.                                |
|                      | Vitest                  | 1.6.0                       | Testing framework for Vite projects.                    | For frontend unit tests (Vue.js components, logic).                 |
| **Development Tools** | GitHub Actions          | N/A                         | Automation platform for CI/CD.                          | For automated builds, tests, and deployments.                          |