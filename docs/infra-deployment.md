# Infrastructure and Deployment Overview

This section outlines the infrastructure, hosting choices, and deployment strategy for the Playlist Intelligence Agent MVP, prioritizing cost-effectiveness and simplicity for a solo-developer, personal-use application.

## Cloud Provider(s) & Hosting Platforms:
* Backend (FastAPI Application) & Database (PostgreSQL + pgvector): The primary target for hosting will be **Koyeb**. Koyeb offers free/low-cost tiers suitable for Python applications (like FastAPI) and managed PostgreSQL databases (which can support pgvector). This aligns with the PRD's emphasis on cost-effectiveness.
* Frontend (Vue.js SPA): Will be hosted on a platform optimized for static site/SPA hosting, such as **Vercel** or **Netlify**. These platforms typically offer generous free tiers and excellent integration with GitHub for CI/CD. The final choice between Vercel and Netlify can be made based on ease of use and specific feature preferences at the time of implementation.

## Core Services Used:
* **Koyeb:**
    * Managed compute for running the Dockerized FastAPI backend application.
    * Managed PostgreSQL service with support for the pgvector extension.
* **Vercel/Netlify:**
    * Static hosting for the Vue.js frontend application.
    * CDN for fast global delivery of frontend assets.
    * Serverless functions (potentially, if needed for simple backend tasks related to the frontend, though the primary backend is on Koyeb).
* **Spotify Web API:** External service for all music data and playlist interactions.
* **LLM API (e.g., Google Gemini):** External service for generating justifications.
* **GitHub:** For source code management.
* **GitHub Actions:** For CI/CD automation.

## Infrastructure as Code (IaC):
For the MVP, formal IaC tools like `Terraform` or `AWS CDK` are not a primary focus due to the simplicity of the chosen PaaS/static hosting platforms.
* `Docker` and `Docker Compose` will be used for defining the local development environment and for containerizing the backend application for deployment on Koyeb.
* The `Dockerfile` for the backend and a `docker-compose.yml` for local setup will serve as the primary infrastructure definitions.
* The project structure includes an `infra/` directory placeholder, which could be used for any simple configuration scripts or future IaC if the project scales.

## Deployment Strategy:
* **Continuous Integration (CI):** `GitHub Actions` will be used to automate builds and run tests (backend unit tests with `Pytest`, frontend unit tests with `Vitest`) on every push to the main branch (or feature branches if used). (Based on PRD Sec 5c).
* **Continuous Deployment (CD):**
    * **Frontend:** Automated deployment to Vercel/Netlify will be triggered by pushes to the main branch via `GitHub Actions` or direct integration provided by these platforms.
    * **Backend:** Deployment to Koyeb will likely be semi-automated via `GitHub Actions`. This could involve building the Docker image, pushing it to a container registry (e.g., GitHub Container Registry or Docker Hub), and then triggering a service update on Koyeb. For MVP, a manual trigger of the deployment workflow from `GitHub Actions` after successful CI on the main branch is acceptable.
    * Database migrations (`Alembic`) will need to be run as part of the backend deployment process, before the new application version is live. This can be a step in the `GitHub Actions` workflow that connects to the Koyeb database.
* **Environments:**
    * **Local Development:** Developers will use `Docker Compose` to run the backend, PostgreSQL database, and the frontend development server on their local machines.
    * **Production:** A single, cloud-hosted "production" environment will be used for this personal application. This will consist of the services hosted on Koyeb (backend, DB) and Vercel/Netlify (frontend).
    * No separate staging environment is planned for the MVP.
* **Environment Promotion:**
    * Not strictly applicable in a traditional sense due to having only one cloud environment (Production) for the MVP.
    * Code from feature branches (if used) will be merged to the main branch after review and passing CI tests.
    * Deployment to the production environment from the main branch will occur after successful CI, potentially with a manual approval step in the `GitHub Actions` workflow for the backend.
* **Rollback Strategy:**
    * **Frontend:** Vercel/Netlify provide built-in mechanisms to instantly roll back to previous deployments.
    * **Backend (Koyeb):**
        * If using Docker images tagged with Git commit SHAs, rollback can be achieved by re-deploying a previously known good Docker image tag via the Koyeb dashboard or a re-run of a specific `GitHub Actions` deployment workflow.
        * Database rollbacks would be handled by `Alembic` (e.g., `alembic downgrade`), but this requires careful planning and is typically reserved for schema issues, not data correction. Data issues would require manual intervention or application-level fixes.