# API Reference (Internal APIs Provided)

This section details the internal APIs exposed by the Python/FastAPI backend for consumption by the Vue.js frontend [user_input_file_0 source: 103].

**Base URL:** All backend API endpoints will be prefixed with `/api/v1` [user_input_file_0 source: 104].

**Authentication:** Unless otherwise specified (e.g., for the initial login redirect), all endpoints require a valid application-specific JWT transmitted via a secure, HttpOnly cookie, as defined in Section 5.2 of the Main Architecture Document [user_input_file_0 source: 105]. The APIControllerLayer will validate this JWT [user_input_file_0 source: 106].

## Group 1: Authentication & User Context
**Purpose:** To manage user authentication with Spotify, handle callbacks, provide user status, and allow session invalidation [user_input_file_0 source: 106].

### GET /api/v1/auth/login
* **Description:** Initiates the Spotify OAuth 2.0 Authorization Code Flow. Redirects the user to Spotify's authorization page [user_input_file_0 source: 107].
* **Request Parameters:** None [user_input_file_0 source: 108].
* **Request Body Schema:** None [user_input_file_0 source: 108].
* **Success Response Schema (Code: 302 Found):** Redirect to Spotify authorization URL [user_input_file_0 source: 108].
* **Error Response Schema(s):**
    * 500 Internal Server Error: If backend fails to construct the Spotify redirect URL [user_input_file_0 source: 109]. JSON `{"detail": "Error preparing Spotify authentication."}` [user_input_file_0 source: 110].

### GET /api/v1/auth/callback
* **Description:** Handles the callback from Spotify after user authorization [user_input_file_0 source: 110]. Exchanges the authorization code for Spotify access and refresh tokens, stores them securely, and then redirects the user to a frontend route (e.g., the main dashboard or playlist selection screen) [user_input_file_0 source: 111].
* **Request Parameters:**
    * `code` (query string): The authorization code provided by Spotify [user_input_file_0 source: 112].
    * `state` (query string, optional but recommended for CSRF protection): The state parameter [user_input_file_0 source: 113].
* **Request Body Schema:** None [user_input_file_0 source: 113].
* **Success Response Schema (Code: 302 Found):** Redirect to a frontend URL (e.g., /dashboard or /select-playlist) [user_input_file_0 source: 114]. Sets the application JWT cookie [user_input_file_0 source: 115].
* **Error Response Schema(s):**
    * 400 Bad Request: If code is missing or invalid, or state mismatch [user_input_file_0 source: 115]. JSON `{"detail": "Invalid authorization code or state."}` [user_input_file_0 source: 116].
    * 500 Internal Server Error: If token exchange with Spotify fails or tokens cannot be stored [user_input_file_0 source: 116]. JSON `{"detail": "Failed to finalize Spotify authentication."}` [user_input_file_0 source: 117].

### GET /api/v1/auth/status
* **Description:** Retrieves the current authentication status of the user and basic user information if authenticated [user_input_file_0 source: 117]. Supports PRD Story 5.7 AC2, AC4 [user_input_file_0 source: 118].
* **Request Parameters:** None [user_input_file_0 source: 118].
* **Request Body Schema:** None [user_input_file_0 source: 119].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 119]
    ```json
    // If authenticated:
    {
      "isAuthenticated": true,
      "user": {
        "spotifyUserId": "string", // User's Spotify ID
        "displayName": "string"    // User's Spotify display name
      },
      "managedPlaylist": { // Null if no playlist is configured yet
        "id": "string", // Internal DB ID of the managed playlist
        "spotifyPlaylistId": "string",
        "name": "string"
      }
    }
    // If not authenticated or token invalid/expired:
    {
      "isAuthenticated": false,
      "user": null,
      "managedPlaylist": null
    }
    ```
* **Error Response Schema(s):**
    * 500 Internal Server Error: If there's an issue fetching status [user_input_file_0 source: 120].

### POST /api/v1/auth/logout
* **Description:** Invalidates the user's current session by clearing the application JWT cookie [user_input_file_0 source: 121].
* **Request Parameters:** None [user_input_file_0 source: 121].
* **Request Body Schema:** None [user_input_file_0 source: 122].
* **Success Response Schema (Code: 200 OK):** JSON `{"message": "Successfully logged out."}` [user_input_file_0 source: 122] (Cookie will be cleared via Set-Cookie header with expiry in the past) [user_input_file_0 source: 123].
* **Error Response Schema(s):** None typically, as this is a best-effort clearing [user_input_file_0 source: 124].

## Group 2: Playlist Configuration
**Purpose:** To allow the user to view their Spotify playlists, select one to be managed by the Playlist Intelligence Agent, and retrieve details of the currently managed playlist [user_input_file_0 source: 125].

### GET /api/v1/playlists
* **Description:** Retrieves a list of the authenticated user's Spotify playlists [user_input_file_0 source: 126]. Supports PRD Story 1.5 AC1 and Story 5.2 AC2 [user_input_file_0 source: 127].
* **Request Parameters:** None [user_input_file_0 source: 127].
* **Request Body Schema:** None [user_input_file_0 source: 127].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 128]
    ```json
    {
      "playlists": [
        {
          "id": "string", // Spotify Playlist ID
          "name": "string", // Playlist Name
          "trackCount": "integer" // Number of tracks in the playlist
        }
        // ... more playlists
      ]
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized: If the user is not authenticated or their Spotify token is invalid [user_input_file_0 source: 128].
    * 500 Internal Server Error: If there's an issue communicating with Spotify or processing the request [user_input_file_0 source: 129]. JSON `{"detail": "Failed to retrieve user playlists from Spotify."}` [user_input_file_0 source: 130].

### POST /api/v1/playlists/managed
* **Description:** Sets or updates the Spotify playlist to be managed by the Playlist Intelligence Agent for the authenticated user [user_input_file_0 source: 131]. Supports PRD Story 5.2 AC6, AC7 [user_input_file_0 source: 132].
* **Request Body Schema:** JSON `{"spotifyPlaylistId": "string"}` // The Spotify ID of the playlist to be managed [user_input_file_0 source: 132].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 132]
    ```json
    {
      "message": "Managed playlist configured successfully.",
      "managedPlaylist": {
        "id": "string", // Internal DB ID of the managed playlist record
        "spotifyPlaylistId": "string",
        "name": "string" // Name of the configured playlist (fetched from Spotify or cache)
      }
    }
    ```
* **Error Response Schema(s):**
    * 400 Bad Request: If spotifyPlaylistId is missing or invalid [user_input_file_0 source: 132].
    * 401 Unauthorized: If the user is not authenticated [user_input_file_0 source: 133].
    * 404 Not Found: If the provided spotifyPlaylistId does not exist or is not accessible by the user on Spotify [user_input_file_0 source: 133].
    * 500 Internal Server Error: If there's an issue storing the configuration or fetching playlist details [user_input_file_0 source: 134].

### GET /api/v1/playlists/managed
* **Description:** Retrieves details of the Spotify playlist currently configured for management by the agent, including a high-level summary of its calculated "Playlist DNA" [user_input_file_0 source: 135]. Supports PRD Story 5.3 AC2, Story 5.7 AC3, and PRD FR 7.3 [user_input_file_0 source: 136].
* **Request Parameters:** None [user_input_file_0 source: 136].
* **Request Body Schema:** None [user_input_file_0 source: 137].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 137]
    ```json
    // If a playlist is configured:
    {
      "isConfigured": true,
      "managedPlaylist": {
        "id": "string", // Internal DB ID of the managed playlist record
        "spotifyPlaylistId": "string",
        "name": "string",
        "owner": "string", // Playlist owner's display name
        "description": "string", // Playlist description
        "trackCount": "integer",
        "lastSyncedAt": "string | null", // ISO 8601 timestamp of the last overall sync operation
        "dnaSummary": { // This object will be null if DNA has not been computed yet
          "lastCalculatedAt": "string | null", // ISO 8601 timestamp of the last DNA calculation
          "topArtists": [ // e.g., Top 3-5 artists
            { "name": "string" }
          ],
          "topGenres": [ // e.g., Top 3-5 genres
            { "name": "string" }
          ],
          "keyAudioFeatures": [ // e.g., 1-3 key features
            {
              "featureName": "string", // e.g., "Energy", "Valence", "Tempo"
              "averageValue": "number", // e.g., 0.75 (for energy/valence) or 120 (for tempo)
              "qualitativeDescription": "string" // e.g., "High", "Positive", "Moderately Fast"
            }
          ]
        }
      }
    }
    // If no playlist is configured:
    {
      "isConfigured": false,
      "managedPlaylist": null
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized: If the user is not authenticated [user_input_file_0 source: 140].
    * 500 Internal Server Error: If there's an issue retrieving the managed playlist details [user_input_file_0 source: 141].

## Group 3: Dashboard & Synchronization
**Purpose:** To provide the frontend with current queue statuses, allow triggering of the main data synchronization and analysis process, and to check the status of an ongoing or completed sync operation [user_input_file_0 source: 142].

### GET /api/v1/sync/queues/status
* **Description:** Retrieves the current number of items pending review in the Discovery Queue and Removal Review Queue for the authenticated user's managed playlist [user_input_file_0 source: 143]. Supports PRD Story 5.3 AC3 [user_input_file_0 source: 144].
* **Request Parameters:** None [user_input_file_0 source: 144].
* **Request Body Schema:** None [user_input_file_0 source: 144].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 145]
    ```json
    {
      "discoveryQueueCount": "integer", // Number of items pending in Discovery Queue
      "removalQueueCount": "integer"    // Number of items pending in Removal Queue
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 145].
    * 404 Not Found: If no managed playlist is configured [user_input_file_0 source: 146].
    * 500 Internal Server Error [user_input_file_0 source: 146].

### POST /api/v1/sync/trigger
* **Description:** Initiates a full, asynchronous synchronization process for the authenticated user's managed playlist [user_input_file_0 source: 147]. This includes "Playlist DNA" update, new release discovery, and removal suggestion generation. Returns a syncId to track the operation [user_input_file_0 source: 148]. Supports PRD Story 5.3 AC5 [user_input_file_0 source: 149].
* **Request Parameters:** None [user_input_file_0 source: 149].
* **Request Body Schema:** None [user_input_file_0 source: 149].
* **Success Response Schema (Code: 202 Accepted):** JSON [user_input_file_0 source: 150]
    ```json
    {
      "syncId": "string", // A unique ID for this synchronization task
      "message": "Synchronization process initiated."
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 150].
    * 404 Not Found: If no managed playlist is configured [user_input_file_0 source: 151].
    * 409 Conflict: If a sync process is already running for this user/playlist [user_input_file_0 source: 151].
    * 500 Internal Server Error: If the sync process cannot be initiated [user_input_file_0 source: 152].

### GET /api/v1/sync/status/{sync_id}
* **Description:** Retrieves the status and progress of a specific synchronization task initiated by /sync/trigger [user_input_file_0 source: 153]. Supports PRD NFR 1.4 and UI/UX sync modal [user_input_file_0 source: 154].
* **Request Parameters:**
    * `sync_id` (path parameter): The ID of the sync task [user_input_file_0 source: 154].
* **Request Body Schema:** None [user_input_file_0 source: 155].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 155]
    ```json
    {
      "syncId": "string",
      "status": "string", // e.g., "Pending", "Running_DnaAnalysis", "Running_Discovery", "Running_SuggestionGeneration", "Completed_Success", "Completed_WithErrors", "Failed"
      "progressMessage": "string | null", // User-friendly message about current stage
      "startedAt": "string | null", // ISO 8601 timestamp
      "completedAt": "string | null", // ISO 8601 timestamp
      "details": { // Optional, present on completion or error
        "recommendationsFound": "integer | null",
        "removalsSuggested": "integer | null",
        "errors": [ // List of error messages if status is "Completed_WithErrors" or "Failed"
          { "stage": "string", "message": "string" }
        ]
       }
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 156].
    * 404 Not Found: If the sync_id is not found or does not belong to the user [user_input_file_0 source: 157].
    * 500 Internal Server Error: If there's an issue fetching the sync status [user_input_file_0 source: 158].

## Group 4: Review Queues & Actions
**Purpose:** To enable the user to fetch and review pending song recommendations and removal suggestions, and to submit their actions (approve, reject, keep) on these items [user_input_file_0 source: 159].

### GET /api/v1/reviews/recommendations
* **Description:** Retrieves a list of pending song recommendations for the authenticated user's managed playlist, ordered newest first [user_input_file_0 source: 160]. Supports PRD Story 5.4 AC2 [user_input_file_0 source: 161].
* **Request Parameters:**
    * `limit` (query, optional, integer, default: 10): Maximum number of recommendations to return [user_input_file_0 source: 161].
    * `offset` (query, optional, integer, default: 0): Number of recommendations to skip (for pagination) [user_input_file_0 source: 162].
* **Request Body Schema:** None [user_input_file_0 source: 162].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 163]
    ```json
    {
      "recommendations": [
        {
          "id": "string", // Internal DB ID of the recommendation
          "spotifyTrackId": "string",
          "trackName": "string",
          "artists": [ { "name": "string" } ],
          "albumName": "string",
          "albumReleaseDate": "string", // ISO 8601 YYYY-MM-DD
          "previewUrl": "string | null", // URL to Spotify track preview (if available)
          "spotifyTrackUrl": "string", // URL to the track on Spotify
          "justification": "string", // AI-generated justification
          "similarityScore": "number | null", // If applicable
          "discoveredAt": "string" // ISO 8601 timestamp
        }
        // ... more recommendations
      ],
      "totalPending": "integer", // Total number of pending recommendations
      "limit": "integer",
      "offset": "integer"
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 164].
    * 404 Not Found: If no managed playlist is configured [user_input_file_0 source: 165].
    * 500 Internal Server Error [user_input_file_0 source: 165].

### POST /api/v1/reviews/recommendations/{recommendation_id}/action
* **Description:** Submits a user's action (approve for addition or reject) for a specific song recommendation [user_input_file_0 source: 166]. Supports PRD Story 5.4 AC8-AC11 [user_input_file_0 source: 167].
* **Request Parameters:**
    * `recommendation_id` (path parameter): The ID of the recommendation being actioned [user_input_file_0 source: 167].
* **Request Body Schema:** JSON `{"action": "string"}` // Enum: "approve_for_addition", "reject" [user_input_file_0 source: 168].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 168]
    ```json
    {
      "id": "string", // recommendation_id
      "status": "string", // New status, e.g., "approved_for_addition", "rejected"
      "message": "Recommendation action processed successfully."
    }
    ```
* **Error Response Schema(s):**
    * 400 Bad Request: If action is invalid or recommendation is not in a reviewable state [user_input_file_0 source: 168].
    * 401 Unauthorized [user_input_file_0 source: 169].
    * 404 Not Found: If recommendation_id is not found [user_input_file_0 source: 169].
    * 500 Internal Server Error [user_input_file_0 source: 169].

### GET /api/v1/reviews/removals
* **Description:** Retrieves a list of pending song removal suggestions for the authenticated user's managed playlist, ordered newest first [user_input_file_0 source: 170]. Supports PRD Story 5.5 AC2 [user_input_file_0 source: 171].
* **Request Parameters:**
    * `limit` (query, optional, integer, default: 10): Maximum number of removal suggestions to return [user_input_file_0 source: 171].
    * `offset` (query, optional, integer, default: 0): Number of suggestions to skip [user_input_file_0 source: 172].
* **Request Body Schema:** None [user_input_file_0 source: 172].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 173]
    ```json
    {
      "removals": [
        {
          "id": "string", // Internal DB ID of the removal suggestion
          "spotifyTrackId": "string",
          "trackName": "string",
          "artists": [ { "name": "string" } ],
          "albumName": "string",
          "previewUrl": "string | null",
          "spotifyTrackUrl": "string",
          "justification": "string", // AI-generated justification
          "reason": "string", // e.g., "vibe_outlier", "release_age_exceeded"
          "deviationScore": "number | null", // If vibe_outlier
          "suggestedAt": "string" // ISO 8601 timestamp
        }
        // ... more removal suggestions
      ],
      "totalPending": "integer", // Total number of pending removal suggestions
      "limit": "integer",
      "offset": "integer"
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 174].
    * 404 Not Found: If no managed playlist is configured [user_input_file_0 source: 175].
    * 500 Internal Server Error [user_input_file_0 source: 175].

### POST /api/v1/reviews/removals/{removal_id}/action
* **Description:** Submits a user's action (approve removal or keep in playlist) for a specific song removal suggestion [user_input_file_0 source: 176]. Supports PRD Story 5.5 AC8-AC11 [user_input_file_0 source: 177].
* **Request Parameters:**
    * `removal_id` (path parameter): The ID of the removal suggestion being actioned [user_input_file_0 source: 177].
* **Request Body Schema:** JSON `{"action": "string"}` // Enum: "approve_removal", "keep_in_playlist" [user_input_file_0 source: 178].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 178]
    ```json
    {
      "id": "string", // removal_id
      "status": "string", // New status, e.g., "remove_approved", "kept_by_user"
      "message": "Removal suggestion action processed successfully."
    }
    ```
* **Error Response Schema(s):**
    * 400 Bad Request: If action is invalid or suggestion is not in a reviewable state [user_input_file_0 source: 178].
    * 401 Unauthorized [user_input_file_0 source: 179].
    * 404 Not Found: If removal_id is not found [user_input_file_0 source: 179].
    * 500 Internal Server Error [user_input_file_0 source: 179].

## Group 5: Playlist Modification & Debugging
**Purpose:** To allow the user to trigger the application of approved changes (additions/removals) to their Spotify playlist, check the status of this operation, and retrieve debugging information like recent errors and sync logs [user_input_file_0 source: 180].

### POST /api/v1/playlists/managed/apply-changes
* **Description:** Initiates an asynchronous task to apply all currently approved song additions and removals to the authenticated user's managed Spotify playlist [user_input_file_0 source: 181]. Returns an applyId to track this operation. Supports PRD Story 5.6 AC4 [user_input_file_0 source: 182].
* **Request Parameters:** None [user_input_file_0 source: 182].
* **Request Body Schema:** None [user_input_file_0 source: 183].
* **Success Response Schema (Code: 202 Accepted):** JSON [user_input_file_0 source: 183]
    ```json
    {
      "applyId": "string", // A unique ID for this "apply changes" task
      "message": "Playlist modification process initiated."
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 183].
    * 404 Not Found: If no managed playlist is configured [user_input_file_0 source: 184].
    * 409 Conflict: If an "apply changes" process is already running or if there are no approved changes to apply [user_input_file_0 source: 184].
    * 500 Internal Server Error: If the process cannot be initiated [user_input_file_0 source: 185].

### GET /api/v1/playlists/managed/apply-changes/status/{apply_id}
* **Description:** Retrieves the status and outcome of a specific "apply changes" task. Supports PRD Story 5.6 AC6 [user_input_file_0 source: 186].
* **Request Parameters:**
    * `apply_id` (path parameter): The ID of the "apply changes" task [user_input_file_0 source: 187].
* **Request Body Schema:** None [user_input_file_0 source: 187].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 188]
    ```json
    {
      "applyId": "string",
      "status": "string", // e.g., "Pending", "Running_Additions", "Running_Removals", "Completed_Success", "Completed_WithPartialErrors", "Failed"
      "startedAt": "string | null", // ISO 8601 timestamp
      "completedAt": "string | null", // ISO 8601 timestamp
      "summary": { // Present on completion
        "songsRequestedForAddition": "integer",
        "songsSuccessfullyAdded": "integer",
        "songsRequestedForRemoval": "integer",
        "songsSuccessfullyRemoved": "integer",
        "individualErrors": [ // List of specific errors if any
          {
            "spotifyTrackId": "string",
            "action": "string", // "add" or "remove"
            "error": "string" // Description of why it failed
          }
        ]
      }
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 189].
    * 404 Not Found: If apply_id is not found [user_input_file_0 source: 190].
    * 500 Internal Server Error [user_input_file_0 source: 190].

### GET /api/v1/debug/errors
* **Description:** Retrieves a list of recent critical system errors logged by the backend. Supports PRD Story 5.8 AC2 [user_input_file_0 source: 191].
* **Request Parameters:**
    * `limit` (query, optional, integer, default: 20): Maximum number of error entries to return [user_input_file_0 source: 192].
* **Request Body Schema:** None [user_input_file_0 source: 192].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 193]
    ```json
    {
      "errors": [
        {
          "timestamp": "string", // ISO 8601 timestamp
          "message": "string", // Error message
          "context": "object | string | null" // Additional context if available
        }
      ]
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 193].
    * 500 Internal Server Error [user_input_file_0 source: 194].

### GET /api/v1/debug/sync-log
* **Description:** Retrieves a summary log of recent synchronization activities. Supports PRD Story 5.8 AC4 [user_input_file_0 source: 194].
* **Request Parameters:**
    * `limit` (query, optional, integer, default: 10): Maximum number of sync log entries to return [user_input_file_0 source: 195].
* **Request Body Schema:** None [user_input_file_0 source: 195].
* **Success Response Schema (Code: 200 OK):** JSON [user_input_file_0 source: 196]
    ```json
    {
      "syncLog": [
        {
          "syncId": "string",
          "startedAt": "string", // ISO 8601 timestamp
          "completedAt": "string | null",
          "status": "string", // e.g., "Completed_Success", "Failed"
          "durationMs": "integer | null",
          "playlistSnapshotIdBefore": "string | null",
          "playlistSnapshotIdAfter": "string | null",
          "dnaRecalculated": "boolean",
          "recommendationsGenerated": "integer",
          "removalsSuggested": "integer",
          "llmCallsMadeCount": "integer",
          "totalLlmTokensUsed": "integer | null",
          "errorSummary": "string | null" // Brief summary if sync failed or had errors
        }
      ]
    }
    ```
* **Error Response Schema(s):**
    * 401 Unauthorized [user_input_file_0 source: 197].
    * 500 Internal Server Error [user_input_file_0 source: 198].