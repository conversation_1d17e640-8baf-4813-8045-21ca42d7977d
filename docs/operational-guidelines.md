# Operational Guidelines

This document consolidates key operational guidelines for the Playlist Intelligence Agent MVP, including error handling, coding standards, testing strategy, and security best practices.

---
## Error Handling Strategy

This section outlines the strategy for handling errors within the Playlist Intelligence Agent backend, ensuring consistent responses, appropriate logging, and resilience where possible.

### General Approach
The backend (FastAPI) will primarily use Python exceptions for error signaling. Custom exception classes will be defined, inheriting from a base `AppException`, to represent specific business logic or operational errors (e.g., `PlaylistNotFoundError`, `SpotifyApiError`, `LlmInteractionError`). This allows for granular error handling.

FastAPI's exception handlers will be used to catch these custom exceptions (and standard FastAPI/Pydantic exceptions) and transform them into consistent JSON error responses for the frontend (typically `{"detail": "error message"}` with appropriate HTTP status codes).

Uncaught exceptions will be handled by a global FastAPI exception handler to return a generic 500 Internal Server Error response, ensuring no raw stack traces are exposed to the client.

### Logging
* **Library/Method:** Python's built-in `logging` module will be used, configured for the FastAPI application with structured JSON logging.
* **Format:** All logs written to standard output (`stdout`/`stderr`) will be formatted as JSON for easier parsing by log management systems provided by the hosting platform. Each log entry will include a timestamp, log level, logger name (module), correlation ID, and the message.
* **Levels:** Standard Python log levels will be utilized: `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`.
* **Configuration:** Log level is controlled via the `LOG_LEVEL` environment variable (default: `INFO`).
* **Context:** Log messages will include relevant contextual information such as:
    * Correlation IDs for request tracing
    * Relevant entity IDs (e.g., `userId`, `spotifyPlaylistId`) where appropriate and safe (no PII like tokens)
    * Name of the operation or service where the error occurred
    * Sanitized input parameters if relevant to the error
    * Full traceback for exceptions at `DEBUG` level, and summarized error info at `ERROR`/`CRITICAL` level

### Specific Handling Patterns
* **External API Calls (Spotify, LLM):**
    * All calls to external services (`SpotifyIntegrationService`, `LLMIntegrationService`) will be wrapped in `try-except` blocks to catch common errors (e.g., connection errors, timeouts, API-specific HTTP errors).
    * **Retries:** For transient errors (e.g., network issues, Spotify 429 Too Many Requests or 5xx errors, LLM rate limits or temporary unavailability), an automatic retry mechanism with exponential backoff and jitter will be implemented. Libraries like `tenacity` can be used for this. A configurable maximum number of retries (e.g., 3-5) will be set.
    * **Timeouts:** Explicit connect and read timeouts will be configured for all external HTTP requests (e.g., via `httpx` client settings).
    * **Error Translation:** Errors from external APIs will be caught and translated into specific custom application exceptions (e.g., `SpotifyApiError`, `LlmApiError`) which can then be handled appropriately by the service layer or transformed into user-facing API error responses. These custom exceptions will store relevant details from the original error (like status code or external error message) for logging and debugging.
    * **Rate Limiting:** The `SpotifyIntegrationService` will respect `Retry-After` headers from Spotify. The application design (manual syncs, batching where possible) aims to minimize hitting rate limits.
* **Internal Errors / Business Logic Exceptions:**
    * Custom exceptions (e.g., `DnaCalculationError`, `InvalidActionError`) will be raised by services when business rules are violated or operations cannot be completed.
    * These will be caught by FastAPI exception handlers and mapped to appropriate HTTP status codes and error messages (e.g., 400 Bad Request, 404 Not Found, 409 Conflict).
    * A unique request ID or correlation ID (generated per API request or per async task like `syncId`) should be logged with errors to help trace issues through the system.
* **Data Validation Errors:** Pydantic validation errors for API request bodies will be automatically handled by FastAPI, returning 422 Unprocessable Entity responses with detailed error information. Internal data validation should also raise specific exceptions.
* **Transaction Management (Database):**
    * For operations involving multiple database writes that must succeed or fail together (e.g., updating multiple tables during the "Apply Changes" process), standard database transactions provided by SQLAlchemy (or the chosen ORM/DB driver) will be used.
    * A `try-except-finally` block structure will be employed: attempt operations, commit on success, rollback on any exception, and ensure resources like DB sessions are properly closed/released.
    * The MVP scope does not anticipate complex distributed transactions requiring a Saga pattern; standard ACID transactions for single database operations will suffice.

---
## Coding Standards

These standards are mandatory for all code generated by AI agents and human developers for the Playlist Intelligence Agent MVP. The goal is to ensure a clean, consistent, maintainable, and understandable codebase.

* **Primary Runtimes:**
    * Backend: Python 3.12.0+
    * Frontend: Node.js 20.12.2 LTS (for build tools and development server like Vite)
* **Style Guide & Linters:**
    * **Python (Backend - FastAPI):**
        * Style Guide: PEP 8 is mandatory.
        * Formatter: `Ruff` will be used for automatic code formatting and linting to ensure consistency. Configuration for `Ruff` will be in `pyproject.toml`.
        * Type Checking: Full type hints are mandatory for all new function signatures, variable declarations, and class attributes.
    * **TypeScript (Frontend - Vue.js with Vite):**
        * Style Guide: A common style guide such as Airbnb JavaScript Style Guide (adapted for TypeScript) or Google TypeScript Style Guide will be chosen and consistently applied. The specific choice will be documented in the frontend's `README.md` and linter configuration.
        * Formatter: `Prettier` will be used for automatic code formatting. Configuration in `.prettierrc.js` or `package.json`.
        * Linter: `ESLint` with appropriate plugins (`@typescript-eslint/eslint-plugin`, `eslint-plugin-vue`) will be used. Configuration in `.eslintrc.js`.
    * Linter rules from these tools are mandatory and must not be disabled inline without explicit justification commented in the code.
* **Naming Conventions:**
    * **Python:**
        * Variables & Functions/Methods: `snake_case` (e.g., `playlist_dna`, `calculate_similarity()`).
        * Classes & Type Aliases: `PascalCase` (e.g., `PlaylistDnaService`, `SpotifyTrack`).
        * Constants: `UPPER_SNAKE_CASE` (e.g., `MAX_RETRIES`).
        * Modules/Packages: `snake_case` (e.g., `spotify_integration.py`, `data_models` package).
        * Files: `snake_case.py` (e.g., `sync_service.py`).
    * **TypeScript/Vue.js:**
        * Variables & Functions/Methods: `camelCase` (e.g., `managedPlaylist`, `getQueueStatus()`).
        * Classes, Interfaces, Type Aliases, Enums: `PascalCase` (e.g., `AuthService`, `QueueStatusDto`, `SyncStatus`).
        * Vue Component Names (script and template): `PascalCase` (e.g., `PlaylistCard.vue`, `<PlaylistCard />`).
        * Constants: `UPPER_SNAKE_CASE` (e.g., `API_BASE_URL`).
        * Files: `kebab-case.ts` or `PascalCase.vue` for Vue components (e.g., `api-client.ts`, `SyncStatusModal.vue`). Specific convention to be set in frontend `README`.
* **File Structure:**
    * Adhere strictly to the layout defined in `docs/project-structure.md`.
    * New files must be placed in the appropriate directory based on their purpose as described in the project structure documentation.
* **Unit Test File Organization:**
    * Python (Backend): Tests will reside in the `backend/tests/` directory, mirroring the `backend/src/app/` structure (e.g., tests for `backend/src/app/services/dna_service.py` will be in `backend/tests/services/test_dna_service.py`). Test files prefixed with `test_`.
    * TypeScript/Vue.js (Frontend): Unit/component tests will be co-located with the source files using a `*.spec.ts` or `*.test.ts` naming convention (e.g., `MyComponent.vue` and `MyComponent.spec.ts` in the same directory) or within a `__tests__` subdirectory.
* **Asynchronous Operations:**
    * Python (FastAPI): `async/await` must be used for all I/O-bound operations within FastAPI path operations and services to leverage its asynchronous capabilities. Standard library `asyncio` will be used.
    * TypeScript (Frontend): `async/await` with Promises must be used for all asynchronous operations, such as API calls (e.g., `getQueueStatus()`).
* **Type Safety:**
    * Python: Full type hints are mandatory for all new function signatures, variable declarations, and class attributes. Avoid `Any` where a more specific type can be used.
    * TypeScript: Strict mode (`"strict": true` in `tsconfig.json`) is mandatory. Avoid `any` type; use `unknown` for truly unknown types and perform type checking, or define appropriate interfaces/types.
    * Type Definitions: Frontend types specific to UI state or components can be defined within the `frontend/src/types/` directory or co-located with components if not widely shared.
* **Comments & Documentation:**
    * Code Comments: Explain *why* something is done, not *what* is being done, for complex or non-obvious logic. Avoid redundant comments.
    * Python: Use standard Python docstrings (e.g., Google style or reStructuredText) for modules, classes, functions, and methods.
    * TypeScript: Use JSDoc/TSDoc comments for functions, methods, classes, and interfaces.
    * READMEs: Each main application directory (`backend/`, `frontend/`) and potentially significant modules/packages should have a `README.md` explaining its purpose, setup, how to run/test, and key architectural decisions specific to it. The root `README.md` covers the overall project.
* **Dependency Management:**
    * Python (Backend): `uv` with `pyproject.toml` and `uv.lock` file for dependency management.
    * TypeScript/Vue.js (Frontend): `npm` with `package.json` and `package-lock.json`.
    * Policy on Adding New Dependencies:
        * Minimize dependencies. Prefer standard library features or existing project dependencies where feasible.
        * Evaluate the new dependency for necessity, maintenance status, security vulnerabilities, and license compatibility.
        * Use package managers for dependency management instead of manually editing configuration files.
    * Versioning Strategy: Pin exact versions for all dependencies to ensure reproducible builds (e.g., `library==1.2.3` in Python, `"library": "1.2.3"` in `package.json`).

### Detailed Language & Framework Conventions
* **Python / FastAPI (Backend) Specifics:**
    * Immutability: Favor immutable data structures where practical. For Pydantic models used in FastAPI, leverage features like `model_config = {'frozen': True}` for DTOs where appropriate if they are not meant to be mutated after creation.
    * Functional vs. OOP: Employ classes for representing services, repositories, and Pydantic/SQLAlchemy models. Use functions for stateless utility operations or within services where appropriate. Prioritize clarity and maintainability.
    * Error Handling Specifics (Language Level): Raise custom exceptions inheriting from a base `AppException` for domain-specific errors, as outlined in Section 12 of Main Architecture Document. Ensure exceptions provide clear context.
    * Resource Management: Always use `with` statements for resources that need to be managed (e.g., database sessions if not handled by FastAPI dependencies, file handles). SQLAlchemy sessions managed by FastAPI dependencies typically handle this automatically for path operations.
    * Module System: Use absolute imports within the `backend/src/app` package where possible for clarity (e.g., `from app.services.user_service import UserService`).
    * Logging Specifics (Language Level): Utilize Python's `logging` module as configured. When logging, provide sufficient context. Avoid logging sensitive information directly (e.g., raw API tokens, passwords).
    * FastAPI Idioms:
        * Use Pydantic models for request/response body validation and serialization.
        * Leverage FastAPI's dependency injection system for services, repositories, and configurations.
        * Organize API endpoints using `APIRouter`.
        * Use `async def` for path operations that perform I/O.
    * SQLAlchemy (if used directly beyond simple repository patterns): Follow SQLAlchemy best practices. Be mindful of session management and query efficiency. Prefer explicit column selections over `SELECT *`.
    * Code Generation Anti-Patterns to Avoid:
        * Avoid overly complex or deeply nested functions/methods.
        * Do not hardcode sensitive information (API keys, secrets); use the ConfigurationService.
        * Avoid reimplementing functionality already well-provided by Python's standard library or selected robust third-party libraries.
* **TypeScript / Vue.js / Vite (Frontend) Specifics:**
    * Immutability: Prefer immutable updates for state managed by Pinia or component state. Use spread syntax (`...`) or immutable update patterns.
    * Functional vs. OOP/Composition API: Strongly prefer Vue 3's Composition API for organizing component logic. Use functions for reusable logic (composables). Classes might be used for services if that pattern is adopted.
    * Error Handling Specifics (Language Level): Use `try...catch` blocks for async operations (e.g., API calls). Handle Promise rejections appropriately.
    * Null/Undefined Handling: Enable and adhere to `strictNullChecks`. Avoid non-null assertion operator (`!`) where possible; prefer explicit checks, optional chaining (`?.`), or nullish coalescing (`??`).
    * Module System: Use ESModules (`import`/`export`) exclusively.
    * Logging Specifics (Language Level): Use `console.log`, `console.warn`, `console.error` appropriately. Avoid excessive `console.log` statements in production builds (linters can help manage this).
    * Vue.js Idioms:
        * Follow Vue 3 best practices (Composition API, `<script setup>`).
        * Define props, emits, and component logic clearly.
        * Use `v-if`/`v-else-if`/`v-else` for conditional rendering, `v-for` for list rendering (with keys).
        * Emit events from child components to communicate with parents (`defineEmits`).
        * Utilize Pinia for global/feature state management as defined in the Frontend Architecture (when detailed).
    * Tailwind CSS: Prefer applying utility classes directly in templates. For complex, reusable styles, consider creating dedicated Vue components that encapsulate those styles or using `@apply` in component style blocks if necessary, but utility-first is the primary approach.
    * Key Library Usage Conventions: Follow the idiomatic usage patterns for Vue Router, Pinia, SwiperJS, etc..
    * Code Generation Anti-Patterns to Avoid:
        * Avoid direct DOM manipulation; let Vue manage the DOM.
        * Avoid overly large components; break them down into smaller, manageable pieces.
        * Do not hardcode API URLs or keys in components; use environment variables or a configuration service module.

---
## Overall Testing Strategy
(Extracted from Main Architecture Document Section 14)

This section outlines the comprehensive testing strategy for the Playlist Intelligence Agent MVP. The goal is to ensure software quality, verify functional requirements, and maintain stability through a combination of automated and manual testing efforts. All AI-generated and human-written code must adhere to this strategy.

* **Tools:** (As defined in Section 6 of Main Architecture Document: Definitive Tech Stack Selections)
    * Backend (Python): `Pytest 8.2.0` for unit and integration tests.
    * Frontend (Vue.js/TypeScript): `Vitest 1.6.0` for unit and component tests.
    * Code Coverage (Python): `pytest-cov` (or equivalent Pytest plugin).
    * Code Coverage (Frontend): Vitest's built-in coverage capabilities (e.g., using `v8` or `istanbul`).
* **Unit Tests:**
    * Scope:
        * Backend: Individual functions, methods, classes (services, repositories, core logic components) will be tested in isolation. Tests will focus on business logic, algorithms, data transformations, and validation rules, including edge cases and negative scenarios. Comprehensive unit tests are required for all new backend code.
        * Frontend: Individual Vue components, Composable functions, Pinia store modules, and any significant or complex frontend-specific utility functions will be tested in isolation. Tests will focus on component rendering with various props, user interactions (simulated), event emission, internal state changes, and store logic.
    * Location:
        * Backend: As defined in Section 13 (Coding Standards) - `backend/tests/`, mirroring the `backend/src/app/` structure.
        * Frontend: As defined in Section 13 - co-located with source files (`*.spec.ts` or `*.test.ts`) or in `__tests__` subdirectories.
    * Mocking/Stubbing:
        * Backend: Pytest's built-in fixtures and mocking capabilities (e.g., `unittest.mock.patch`, `pytest-mock`) will be used to mock external dependencies like the `SpotifyIntegrationService`, `LLMIntegrationService`, and direct `PersistenceLayer` calls within service tests.
        * Frontend: Vitest's mocking features (`vi.mock`) will be used to mock imported modules, API service calls, and child components. Pinia stores will be mocked or provided with initial state for testing components in isolation.
    * AI Agent Responsibility: AI developer agents are expected to generate comprehensive unit tests covering all public methods/functions, significant logic paths, common use cases, edge cases, and error conditions for any new or modified code.
* **Integration Tests:**
    * Scope:
        * Backend: Focus on testing the interaction between key components or services *within the backend application boundary*, and critically, interactions with the actual Spotify API (for response schema validation).
            * Example 1: Test an API endpoint (e.g., `/api/v1/playlists`) through the `APIControllerLayer` to the relevant service, mocking only the direct database call if necessary, or using a test database for more comprehensive integration.
            * Example 2 (Spotify API Interaction): Specific tests will be designed to call actual (read-only) Spotify API endpoints (using test credentials if possible, or carefully managed developer credentials for non-modifying calls) to ensure the application correctly parses expected data fields from Spotify's responses, particularly for new release discovery and track feature extraction. This helps catch issues if Spotify API response structures change.
    * Location: Backend integration tests will reside in `backend/tests/integration/`.
    * Environment: For tests involving the database, a separate test database (e.g., a local Dockerized PostgreSQL instance specifically for testing) will be used, with schema managed by Alembic. For Spotify API interaction tests, these will be clearly marked and may require network access. `Testcontainers` might be considered for managing DB instances if local setup becomes complex.
    * AI Agent Responsibility: AI agents may be tasked with generating integration tests for key service interactions or API endpoints, particularly for validating the flow of data between internal components and for the Spotify API contract tests.
* **End-to-End (E2E) Tests:**
    * Scope: Automated E2E tests for the UI are *not* a requirement for the MVP. UI functionality will be primarily validated through *manual testing by the user* (the primary stakeholder and developer). A checklist of key user flows (derived from PRD Epics and UI/UX flows) will guide this manual testing.
    * Consideration for "Playlist DNA Accuracy Validation": While not a formal E2E test, the development process should include methods to manually assess the plausibility and consistency of the generated "Playlist DNA" profiles against known playlist characteristics.
* **Test Coverage:**
    * Target: While a specific percentage target (e.g., 80%) is a good aim for unit test line/branch coverage, the primary focus will be on the *quality and effectiveness of tests* in covering critical functionality, business logic, and edge cases, rather than purely achieving a numerical target.
    * Measurement: Coverage reports will be generated using `pytest-cov` for the backend and Vitest's built-in tools for the frontend. These can be reviewed periodically.
* **Mocking/Stubbing Strategy (General):**
    * Prefer real objects over mocks where it doesn't introduce external dependencies or slow down tests significantly (e.g., using real Pydantic models).
    * When mocking, mock at the boundary of your system or component.
    * Ensure mocks are simple and accurately reflect the contract of the mocked dependency.
    * Strive for tests that are fast, reliable, and isolated.
* **Test Data Management:**
    * Unit Tests: Use small, inline, hardcoded data specific to each test case.
    * Integration Tests:
        * May use Pytest fixtures to set up and tear down database state or provide common test data.
        * For Spotify API contract tests, predefined, stable Spotify entity IDs (e.g., for specific tracks or artists whose metadata is unlikely to change drastically) might be used.
        * No complex test data generation tools are planned for MVP.

---
## Security Best Practices
(Extracted from Main Architecture Document Section 15)

This section outlines mandatory security best practices to be adhered to throughout the development and deployment of the Playlist Intelligence Agent MVP. The goal is to protect user data, ensure secure communication, and mitigate common web application vulnerabilities.

* **Input Sanitization/Validation:**
    * Backend (FastAPI): All incoming data from API requests (query parameters, path parameters, request bodies) *must* be rigorously validated using Pydantic models, as automatically facilitated by FastAPI. This includes type checking, format validation (e.g., for UUIDs, dates), and constraints (e.g., string lengths, numerical ranges if applicable). No unvalidated external data should be processed by business logic.
    * Frontend (Vue.js): While primary validation occurs on the backend, basic client-side validation should be implemented for user inputs (e.g., in forms on the conceptual Settings screen) to improve user experience. However, client-side validation is not a substitute for backend validation.
* **Output Encoding:**
    * Frontend (Vue.js): Vue.js, by default, sanitizes content when using `{{ mustache }}` interpolations or `v-bind`, which helps prevent Cross-Site Scripting (XSS). Direct use of `v-html` *must* be avoided unless the content is from a trusted source and has been explicitly sanitized using a library like DOMPurify (if such a need arises, which is unlikely for this MVP's UI).
    * Backend (API Responses): API responses are JSON. FastAPI and Pydantic handle proper JSON serialization. Ensure that any user-generated content that might be reflected in API responses (though minimal in this app) is appropriately handled to prevent injection if it were ever rendered as HTML directly by a misconfigured client (generally not an issue for SPA frontends consuming JSON).
* **Secrets Management:**
    * Spotify Tokens (`access_token`, `refresh_token`): As per PRD NFR 6.1 and database schema for `users`, these tokens *must* be encrypted before being stored in the PostgreSQL database. The application backend will handle encryption before storage and decryption upon retrieval using a strong encryption algorithm and a securely managed encryption key.
    * Application Secrets (LLM API Key, DB Credentials, JWT Secret Key, Encryption Key): These *must not* be hardcoded in the source code or committed to the Git repository. They will be managed via environment variables, loaded by the ConfigurationService (`backend/src/app/config.py`). For local development, these will be stored in a git-ignored `.env` file. For production deployment on Koyeb, they will be configured as secure environment variables in the Koyeb service settings.
    * Logging: Secrets (tokens, API keys, passwords) *must never* be logged, as specified in Section 12 (Error Handling Strategy) of Main Architecture Document.
* **Dependency Security:**
    * Vulnerability Scanning: Regularly scan project dependencies (both backend Python packages and frontend npm packages) for known vulnerabilities using tools like `pip-audit` or `safety` (for Python) and `npm audit` or Snyk (for frontend). This should be integrated into the CI pipeline (GitHub Actions).
    * Update Policy: High and critical severity vulnerabilities in dependencies must be addressed promptly, either by updating to a patched version or replacing the dependency if a fix is unavailable.
    * Vetting New Dependencies: Before adding any new third-party library, evaluate its necessity, security track record, maintenance status, and license.
* **Authentication & Authorization Checks:**
    * Authentication (App Session): As defined in Section 5.2 and Section 8 (API Reference) of Main Architecture Document, most backend API endpoints (except public ones like `/auth/login`, `/auth/callback`) *must* be protected and require a valid application-specific JWT. This JWT is transmitted via a secure, HttpOnly, SameSite cookie. The APIControllerLayer will validate this JWT for incoming requests.
    * Authorization (MVP Scope): The MVP is designed for a single user managing their own data. Therefore, complex role-based authorization is not required. Authorization checks will primarily ensure that API requests made by an authenticated user operate only on data associated with that user (e.g., their managed playlist, their DNA, their review queues). This will be enforced in the service layer by filtering data based on the authenticated `user_id`.
* **Principle of Least Privilege (PoLP):**
    * Database User: The PostgreSQL user account configured for the backend application should have only the necessary permissions (SELECT, INSERT, UPDATE, DELETE) on the application's tables. It should not have superuser privileges.
    * Cloud Service Permissions (Koyeb, Vercel/Netlify): If deploying using specific IAM roles or service accounts, these should be granted only the minimum permissions required for the application to run and deploy (e.g., permissions to pull images, run services, access the database, but not to manage unrelated cloud resources).
* **API Security (General):**
    * HTTPS: All communication between the frontend, backend, and external services (Spotify, LLM) *must* use HTTPS. Koyeb and Vercel/Netlify typically provide and enforce HTTPS by default for hosted applications.
    * HTTP Security Headers: Configure the backend (FastAPI middleware or via a reverse proxy if used by the hosting platform) to send security-enhancing HTTP headers such as:
        * `Strict-Transport-Security` (HSTS)
        * `X-Content-Type-Options: nosniff`
        * `X-Frame-Options: DENY` (or `SAMEORIGIN` if framing from same origin is needed)
        * `Content-Security-Policy` (CSP) (A restrictive CSP should be considered to mitigate XSS further, though its complexity might be deferred post-MVP if basic Vue.js XSS protection is deemed sufficient initially).
    * Rate Limiting & Throttling (API): While the MVP is single-user, if a more public version were considered, API rate limiting would be essential. For MVP, this is a lower priority but good to note for future. FastAPI has middleware options for this.
* **Error Handling & Information Disclosure:**
    * As defined in Section 12 (Error Handling Strategy) of Main Architecture Document, detailed error messages or stack traces *must not* be exposed to the end-user in API responses. Generic error messages with a correlation ID (like `syncId` or a request ID) should be used, while detailed errors are logged server-side (in the database for critical errors and/or platform logs).
* **Regular Security Audits/Testing (MVP Scope):**
    * Formal penetration testing or third-party security audits are out of scope for this personal MVP. Security will be maintained through adherence to these best practices, regular dependency scanning, and code reviews focusing on security aspects.
* **Session Management:**
    * The application uses JWTs stored in HttpOnly, Secure, SameSite cookies for session management.
    * JWTs will have a reasonably short expiry (e.g., 15-60 minutes for access tokens, if a separate access/refresh token pattern were used internally; for a simple session JWT, a session expiry or a longer expiry managed by re-authentication might be used). The Spotify access token itself has its own expiry, managed by the backend. Our application JWT ensures the user is logged into *our application*.
    * Secure logout (Section 8, Group 1 API of Main Architecture Document) will clear the session cookie.