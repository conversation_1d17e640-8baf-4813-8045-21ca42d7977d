# Detailed Frontend Directory Structure

The frontend application will reside within the `frontend/` directory of the Monorepo structure defined in the main Architecture Document (Section 7). The internal structure of `frontend/src/` will be organized to promote modularity, feature encapsulation, and clarity, tailored for a Vue.js/Vite project.

```plaintext
frontend/
├── node_modules/           # Frontend dependencies (git-ignored)
├── public/                 # Static assets (e.g., favicon.ico, robots.txt)
├── src/                    # Frontend source code
│   ├── assets/             # Static assets like images, fonts, global CSS not processed by Tailwind's main entry
│   │   └── styles/
│   │       └── main.css    # Main entry point for Tailwind CSS and any global custom styles
│   ├── components/         # Globally reusable UI components
│   │   ├── common/         # Truly generic components (Button, Modal, Spinner, Input)
│   │   └── layout/         # Layout components (AppHeader, AppSidebar, MainLayout)
│   ├── composables/        # Reusable Vue Composition API functions (e.g., useAuth.ts, useApiClient.ts)
│   ├── features/           # Feature-specific modules, each potentially containing its own components, stores, services, views
│   │   └── auth/
│   │       ├── components/ # Components specific to the auth feature
│   │       ├── views/      # Views/Pages specific to the auth feature (e.g., LoginCallbackView.vue)
│   │       └── AuthStore.ts # Pinia store for authentication state
│   │   └── playlist/
│   │       ├── components/ # Components for playlist selection, display
│   │       ├── views/      # PlaylistSelectionView.vue
│   │       └── PlaylistStore.ts
│   │   └── reviewQueue/
│   │       ├── components/ # SongCard.vue, SwipeWrapper.vue
│   │       ├── views/      # DiscoveryQueueView.vue, RemovalQueueView.vue
│   │       └── ReviewQueueStore.ts
│   ├── router/             # Vue Router configuration
│   │   └── index.ts        # Route definitions, navigation guards
│   ├── services/           # API interaction services (wrappers around native fetch)
│   │   ├── apiClient.ts    # Base API client setup
│   │   ├── authService.ts
│   │   ├── playlistService.ts
│   │   └── syncService.ts
│   ├── store/              # Pinia store setup and global/core stores (if any not in features/)
│   │   └── index.ts        # Main Pinia store creation and plugin setup
│   ├── types/              # Global TypeScript type definitions/interfaces for the frontend
│   │   └── apiTypes.ts     # Types for API request/response payloads (can mirror backend DTOs)
│   │   └── index.ts
│   ├── views/ / pages/     # Top-level page components / route views (alternative to feature-based views)
│   │   ├── DashboardView.vue
│   │   ├── SettingsView.vue
│   │   ├── DebugView.vue
│   │   └── NotFoundView.vue
│   ├── App.vue             # Root Vue component (using <script lang="ts">)
│   └── main.ts             # Frontend application entry point (Vue app initialization, router, Pinia)
├── tests/                  # Frontend tests (unit, component) - Vitest configuration and potentially e2e setup
│   └── unit/
│       └── example.spec.ts
├── index.html              # Main HTML file for Vite
├── package.json            # Frontend project manifest & dependencies
├── vite.config.ts          # Vite configuration
├── tsconfig.json           # TypeScript configuration for the frontend
├── tailwind.config.js      # Tailwind CSS configuration
├── postcss.config.js       # PostCSS configuration (for Tailwind)
└── README.md               # Frontend specific README
```

## Notes on Frontend Structure:
* **Modularity:** The `src/features/` directory is key for encapsulating functionality. Each feature should be as self-contained as possible.
* **Reusability:** Globally reusable components go into `src/components/common/` or `src/components/layout/`. Reusable logic goes into `src/composables/`.
* **Clarity:** This structure aims to make it easy to locate files and understand the applications architecture. Adherence to this structure is mandatory for new file creation. All `.vue` files will use `<script lang="ts" setup>` for Composition API with TypeScript.