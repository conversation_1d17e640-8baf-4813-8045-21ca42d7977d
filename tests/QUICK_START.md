# Quick Start - Testing Guide

This is a quick reference for running tests in the Playlist Intelligence Agent workspace.

## 🚀 Quick Commands

### Run All Tests
```bash
# From workspace root - runs all backend and script tests
python -m pytest
```

### Backend Tests Only
```bash
# Option 1: From workspace root
python -m pytest backend/tests/

# Option 2: From backend directory (recommended for development)
cd backend
uv run pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 🎯 Common Test Scenarios

### During Backend Development
```bash
cd backend

# Run all tests
uv run pytest

# Run only unit tests (fast)
uv run pytest tests/unit/

# Run specific test file
uv run pytest tests/unit/integrations/test_spotify_service.py

# Run with coverage
uv run pytest --cov=src/app --cov-report=html
```

### During Frontend Development
```bash
cd frontend

# Run tests in watch mode
npm test

# Run tests once
npm run test:run
```

### Before Committing
```bash
# From workspace root - run all tests
python -m pytest

# Check code quality
cd backend && uv run ruff check src/ tests/
cd frontend && npm run lint
```

## 🔧 Troubleshooting

### "ModuleNotFoundError" when running tests
- **From workspace root**: Use `python -m pytest`
- **From backend**: Use `cd backend && uv run pytest`

### "ValidationError" for environment variables
- Ensure `backend/.env` file exists with required variables
- Check the `.env.example` file for required variables

### Tests not discovered
- Check file names start with `test_` or end with `_test.py`
- Ensure `__init__.py` files exist in test directories

## 📁 Test File Locations

```
workspace/
├── backend/tests/           # Backend Python tests
│   ├── unit/               # Fast, isolated tests
│   └── integration/        # Multi-component tests
├── frontend/src/           # Frontend tests (co-located)
│   └── **/*.spec.ts       # Component tests
└── scripts/               # Script validation tests
    └── test_*.py          # Utility script tests
```

## 📖 Full Documentation

For comprehensive testing documentation, see [tests/README.md](./README.md).
