# Testing Documentation - Playlist Intelligence Agent

This document provides comprehensive guidance for testing in the Playlist Intelligence Agent workspace, covering both backend (Python/FastAPI) and frontend (Vue.js/TypeScript) testing strategies.

## Table of Contents

1. [Testing Organization Structure](#testing-organization-structure)
2. [Test Execution Instructions](#test-execution-instructions)
3. [Test Configuration Details](#test-configuration-details)
4. [Test Development Guidelines](#test-development-guidelines)
5. [Troubleshooting Section](#troubleshooting-section)
6. [Dependencies and Setup](#dependencies-and-setup)

## Testing Organization Structure

### Backend Tests (`backend/tests/`)

```
backend/tests/
├── __init__.py                    # Makes tests a Python package
├── conftest.py                    # Backend-specific fixtures and configuration
├── integration/                   # Integration tests
│   ├── __init__.py
│   ├── api/                      # API endpoint integration tests
│   │   └── test_health_endpoint.py
│   └── integrations/             # External service integration tests
│       └── (empty - for future Spotify API integration tests)
└── unit/                         # Unit tests
    ├── __init__.py
    ├── core/                     # Core functionality tests
    │   ├── test_config.py        # Configuration and settings tests
    │   └── test_security.py      # Security utilities tests
    ├── integrations/             # Service layer tests
    │   └── test_spotify_service.py
    └── repositories/             # Data access layer tests
        └── (empty - for future database repository tests)
```

### Frontend Tests

Frontend tests use Vitest and are located within the `frontend/` directory:
- Component tests: Co-located with components (`*.spec.ts` files)
- Integration tests: In `frontend/tests/` (when created)
- Test runner: Vitest (configured in `vite.config.ts`)

### Workspace-Level Tests

- `scripts/`: Contains validation scripts with embedded tests
- Root-level configuration supports multi-project test discovery

## Test Execution Instructions

### Running All Tests (Workspace Root)

```bash
# Run all tests from workspace root
python -m pytest

# Run with verbose output
python -m pytest -v

# Run with coverage reporting
python -m pytest --cov=backend/src/app --cov-report=html
```

### Running Backend Tests Only

```bash
# From workspace root
python -m pytest -m backend

# From backend directory (recommended for backend development)
cd backend
uv run pytest

# Run specific test categories
cd backend
uv run pytest -m unit          # Unit tests only
uv run pytest -m integration   # Integration tests only
uv run pytest tests/unit/core/ # Specific directory
```

### Running Frontend Tests

```bash
# From frontend directory
cd frontend
npm test                # Run tests with Vitest
npm run test:coverage   # Run with coverage (if configured)
```

### Running Specific Test Categories

```bash
# Using markers from workspace root
python -m pytest -m "backend and unit"        # Backend unit tests
python -m pytest -m "backend and integration" # Backend integration tests
python -m pytest -m scripts                   # Script validation tests

# Exclude certain test types
python -m pytest -m "not live_api_test"       # Skip tests requiring live APIs
```

## Test Configuration Details

### Root-Level Configuration

**`pyproject.toml`** (Workspace root):
- Configures pytest for multi-project discovery
- Sets test paths: `["backend/tests", "scripts"]`
- Defines workspace-wide markers and coverage settings
- Handles environment variable loading from `backend/.env`

**`conftest.py`** (Workspace root):
- Sets up Python path for backend imports
- Loads backend environment variables automatically
- Adds project-specific markers based on test location

### Backend-Specific Configuration

**`backend/pyproject.toml`**:
- Backend-specific pytest configuration
- Test dependencies managed through `uv`
- Async test configuration (`asyncio_mode = "auto"`)
- Coverage settings for backend source code

**`backend/conftest.py`**:
- Backend-specific fixtures (mock clients, sample data)
- Database session mocks
- Spotify API mocks

### Environment Variable Handling

Tests automatically load environment variables from `backend/.env`:
- Required for backend tests (Spotify credentials, JWT secrets, etc.)
- Loaded automatically when running from workspace root
- When running from `backend/` directory, uses local `.env` file

## Test Development Guidelines

### Async Test Patterns

Backend tests extensively use async patterns for FastAPI compatibility:

```python
# Correct async test pattern
async def test_async_function():
    result = await some_async_function()
    assert result is not None

# For testing async functions that raise exceptions
async def test_async_exception():
    with pytest.raises(SomeException):
        await some_async_function_that_fails()
```

### Mocking Strategies

**External Dependencies:**
```python
# Mock Spotify API client
@pytest.fixture
def mock_spotify_client():
    client = MagicMock()
    client.current_user_playlists = MagicMock()
    return client

# Mock settings for configuration tests
def test_missing_credentials():
    with patch("app.integrations.spotify_service.settings") as mock_settings:
        mock_settings.SPOTIFY_CLIENT_ID = None
        # Test logic here
```

**Database Mocking:**
```python
# Mock async database session
@pytest.fixture
def mock_db_session():
    session = AsyncMock(spec=AsyncSession)
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    return session
```

### Test Fixture Organization

- **Global fixtures**: In workspace root `conftest.py`
- **Backend fixtures**: In `backend/tests/conftest.py`
- **Test-specific fixtures**: In individual test files
- **Reusable data**: Sample API responses, user data, etc.

### Naming Conventions

- **Test files**: `test_*.py` or `*_test.py`
- **Test classes**: `Test*` (e.g., `TestSpotifyOAuth`)
- **Test functions**: `test_*` (e.g., `test_get_user_playlists_success`)
- **Async tests**: Use `async def test_*` pattern

### File Organization

- Group related tests in classes
- One test file per module/service being tested
- Separate unit and integration tests clearly
- Use descriptive test names that explain the scenario

## Troubleshooting Section

### Import Path Problems

**Issue**: `ModuleNotFoundError` when running tests from different directories

**Solutions:**
```bash
# Ensure you're using the correct command for your location
# From workspace root:
python -m pytest

# From backend directory:
cd backend && uv run pytest

# Check Python path in conftest.py is correctly set
```

### Environment Variable Configuration Errors

**Issue**: `ValidationError` for missing environment variables

**Solutions:**
1. Ensure `backend/.env` file exists and contains required variables
2. Check that root-level `conftest.py` is loading environment variables
3. For backend-only testing, run from `backend/` directory

**Required Environment Variables:**
- `SPOTIFY_CLIENT_ID`
- `SPOTIFY_CLIENT_SECRET` 
- `SPOTIFY_REDIRECT_URI`
- `TOKENS_ENCRYPTION_KEY`
- `JWT_SECRET_KEY`

### Async Test Execution Issues

**Issue**: `RuntimeWarning: coroutine was never awaited`

**Solution**: Ensure async functions are properly awaited:
```python
# Wrong
def test_async_function():
    result = some_async_function()  # Missing await

# Correct  
async def test_async_function():
    result = await some_async_function()
```

### Test Discovery Problems

**Issue**: Tests not being discovered

**Solutions:**
1. Check file naming follows `test_*.py` convention
2. Ensure `__init__.py` files exist in test directories
3. Verify pytest configuration in `pyproject.toml`
4. Run with `--collect-only` to see what's being discovered

## Dependencies and Setup

### Backend Testing Dependencies

Managed through `uv` in `backend/pyproject.toml`:

**Core Testing:**
- `pytest>=8.3.5` - Test framework
- `pytest-asyncio>=1.0.0` - Async test support
- `pytest-mock>=3.14.1` - Mocking utilities

**Coverage and Reporting:**
- `pytest-cov>=6.1.1` - Coverage reporting
- Coverage reports generated in `backend/htmlcov/`

**HTTP Testing:**
- `httpx>=0.28.1` - HTTP client for API testing

### Frontend Testing Dependencies

Managed through `npm` in `frontend/package.json`:
- `vitest^1.6.0` - Test framework (Vite-native)
- `vue-tsc^2.0.13` - TypeScript support for Vue

### Installation Commands

```bash
# Backend dependencies
cd backend
uv sync --dev

# Frontend dependencies  
cd frontend
npm install

# Verify installations
cd backend && uv run pytest --version
cd frontend && npm run test -- --version
```

### IDE Integration

**VS Code:**
- Install Python Test Explorer extension
- Configure test discovery for multi-root workspace
- Set up debugging for async tests

**PyCharm:**
- Configure pytest as test runner
- Set working directory appropriately for backend tests
- Enable async debugging support

## Test Markers Reference

The project uses pytest markers to categorize and filter tests:

| Marker | Description | Usage |
|--------|-------------|-------|
| `unit` | Unit tests (isolated, fast) | `pytest -m unit` |
| `integration` | Integration tests (multiple components) | `pytest -m integration` |
| `backend` | Backend-specific tests | `pytest -m backend` |
| `frontend` | Frontend-specific tests | `pytest -m frontend` |
| `scripts` | Script validation tests | `pytest -m scripts` |
| `live_api_test` | Tests requiring live API access | `pytest -m "not live_api_test"` |

## Performance and Best Practices

### Test Performance Guidelines

1. **Fast Unit Tests**: Keep unit tests under 100ms each
2. **Mock External Dependencies**: Always mock Spotify API, database calls
3. **Parallel Execution**: Use `pytest-xdist` for parallel test execution:
   ```bash
   cd backend && uv run pytest -n auto
   ```
4. **Test Data Management**: Use fixtures for reusable test data

### Security in Tests

- **Never commit real API keys**: Use mock credentials in tests
- **Sanitize test outputs**: Ensure no sensitive data in test logs
- **Environment isolation**: Tests should not affect production data

### Continuous Integration

Tests are designed to run in CI environments:
- All external dependencies are mocked
- Environment variables loaded from `.env` files
- Coverage reports generated for quality gates

## Common Test Patterns

### Testing FastAPI Endpoints

```python
from fastapi.testclient import TestClient

def test_health_endpoint(test_client):
    response = test_client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"
```

### Testing Async Services

```python
async def test_spotify_service(mock_spotify_client):
    result = await get_user_playlists(mock_spotify_client)
    assert isinstance(result, UserPlaylistsResponseDto)
    assert len(result.playlists) >= 0
```

### Testing with Database

```python
async def test_user_repository(mock_db_session):
    user = await create_user(mock_db_session, "test_user")
    mock_db_session.commit.assert_called_once()
```

---

For additional help or questions about testing, refer to the project's operational guidelines or create an issue in the project repository.
