# Utility Scripts

This directory contains utility scripts for the Playlist Intelligence Agent project.

## Available Scripts

### `test_logging_optimization.py`
Tests the enhanced logging system to ensure all optimizations are working correctly.

**Usage:**
```bash
cd scripts
uv run test_logging_optimization.py
```

**Purpose:**
- Validates enhanced logging configuration
- Tests structured JSON logging format
- Checks log level optimization
- Verifies security compliance in logging
- Tests redundancy elimination
- Validates business context enhancement

### `test_security_audit.py`
Security audit test script to verify information disclosure vulnerabilities are fixed.

**Usage:**
```bash
cd scripts
uv run test_security_audit.py
```

**Purpose:**
- Scans Python files for potential information disclosure vulnerabilities
- Checks security implementations
- Validates error handling patterns
- Verifies DTO security
- Performs static analysis of API endpoints

## Notes

- Both scripts automatically reference the backend source code using relative paths
- The scripts are designed to be run from the `scripts/` directory
- `.env` file is required for some tests to run