#!/usr/bin/env python3
"""
Security audit test script to verify information disclosure vulnerabilities are fixed.
This script analyzes the code to ensure no sensitive information is exposed.

Usage:
cd scripts
uv run test_security_audit.py
"""

import os
import re


def scan_file_for_vulnerabilities(file_path: str) -> dict[str, list[str]]:
    """Scan a Python file for potential information disclosure vulnerabilities."""
    vulnerabilities = {
        "exposed_errors": [],
        "sensitive_data": [],
        "debug_info": [],
        "configuration_leaks": [],
    }

    try:
        with open(file_path, encoding="utf-8") as f:
            content = f.read()
            lines = content.split("\n")

        error_patterns = [
            r"raise.*Exception.*\{.*\}",
            r"return.*error.*\{.*\}",
            r'detail.*=.*f".*\{.*\}"',
            r"\.reason",
            r"\.http_status",
        ]

        sensitive_patterns = [
            r"password.*=",
            r"secret.*=",
            r"key.*=",
            r"token.*=",
            r"connection.*string",
            r"database.*url",
        ]

        debug_patterns = [
            r"print\(",
            r"pprint\(",
            r"traceback",
            r"exc_info.*=.*True",
            r"stack.*trace",
        ]

        for i, line in enumerate(lines, 1):
            line_lower = line.lower()

            for pattern in error_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    vulnerabilities["exposed_errors"].append(
                        f"Line {i}: {line.strip()}"
                    )

            for pattern in sensitive_patterns:
                if (
                    re.search(pattern, line, re.IGNORECASE)
                    and "logger" not in line_lower
                ):
                    vulnerabilities["sensitive_data"].append(
                        f"Line {i}: {line.strip()}"
                    )

            for pattern in debug_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    vulnerabilities["debug_info"].append(f"Line {i}: {line.strip()}")

    except Exception as e:
        print(f"Error scanning {file_path}: {e}")

    return vulnerabilities


def check_security_implementations():
    """Check if security measures are properly implemented."""
    print("🔒 Checking Security Implementations...")

    main_file = "../backend/src/app/main.py"
    if os.path.exists(main_file):
        with open(main_file) as f:
            content = f.read()

        security_checks = {
            "security_headers_middleware": "security_headers_middleware" in content,
            "generic_exception_handler": "generic_exception_handler" in content,
            "validation_error_sanitization": "should_expose_detailed_errors" in content,
            "security_config_import": "security_config" in content,
        }

        for check, passed in security_checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check.replace('_', ' ').title()}")

    # Check if security_config.py exists
    security_config_file = "../backend/src/app/core/security_config.py"
    if os.path.exists(security_config_file):
        print("  ✅ Security Configuration Module Exists")
    else:
        print("  ❌ Security Configuration Module Missing")


def scan_api_endpoints():
    """Scan API endpoint files for vulnerabilities."""
    print("\n🛡️ Scanning API Endpoints for Vulnerabilities...")

    api_files = [
        "../backend/src/app/api/auth.py",
        "../backend/src/app/api/health.py",
        "../backend/src/app/api/playlists_router.py",
        "../backend/src/app/api/dependencies.py",
        "../backend/src/app/integrations/spotify_service.py",
    ]

    total_vulnerabilities = 0

    for file_path in api_files:
        if os.path.exists(file_path):
            print(f"\n  📁 Scanning {file_path}...")
            vulnerabilities = scan_file_for_vulnerabilities(file_path)

            file_vuln_count = 0
            for vuln_type, issues in vulnerabilities.items():
                if issues:
                    file_vuln_count += len(issues)
                    print(f"    ❌ {vuln_type.replace('_', ' ').title()}:")
                    for issue in issues[:3]:  # Show first 3 issues
                        print(f"      - {issue}")
                    if len(issues) > 3:
                        print(f"      ... and {len(issues) - 3} more")

            if file_vuln_count == 0:
                print("    ✅ No obvious vulnerabilities found")

            total_vulnerabilities += file_vuln_count
        else:
            print(f"  ⚠️ File not found: {file_path}")

    return total_vulnerabilities


def check_error_handling_patterns():
    """Check for proper error handling patterns."""
    print("\n🚨 Checking Error Handling Patterns...")

    exceptions_file = "../backend/src/app/core/exceptions.py"
    if os.path.exists(exceptions_file):
        with open(exceptions_file) as f:
            content = f.read()

        custom_exceptions = [
            "AppException",
            "UnauthorizedException",
            "NotFoundException",
            "BadRequestException",
        ]
        for exc in custom_exceptions:
            if exc in content:
                print(f"  ✅ {exc} properly defined")
            else:
                print(f"  ❌ {exc} missing")
    else:
        print("  ❌ Exceptions module not found")


def verify_dto_security():
    """Verify that DTOs don't expose sensitive information."""
    print("\n📋 Checking DTO Security...")

    dto_file = "../backend/src/app/core/models/dtos.py"
    if os.path.exists(dto_file):
        with open(dto_file) as f:
            content = f.read()

        if "ErrorResponseDto" in content:
            print("  ✅ Standardized error response DTO exists")
        else:
            print("  ❌ No standardized error response DTO found")

        if 'extra = "forbid"' in content:
            print("  ✅ DTO extra fields forbidden")
        else:
            print("  ⚠️ Consider adding extra='forbid' to DTOs")
    else:
        print("  ❌ DTOs module not found")


def main():
    """Run all security tests."""
    print("🔍 Starting Security Audit Tests...")
    print("=" * 50)

    check_security_implementations()
    total_vulns = scan_api_endpoints()
    check_error_handling_patterns()
    verify_dto_security()

    print("\n" + "=" * 50)
    print("🔍 Security Audit Tests Complete!")

    if total_vulns == 0:
        print("✅ No obvious vulnerabilities detected in static analysis!")
    else:
        print(f"⚠️ Found {total_vulns} potential issues that should be reviewed.")

    print("\nNote: This is a static analysis. Runtime testing with actual requests")
    print("would provide more comprehensive security validation.")


if __name__ == "__main__":
    main()
