<script setup lang="ts">
const backendApiUrl = 'http://localhost:8000';
const loginUrl = `${backendApiUrl}/api/v1/auth/login`;
</script>

<template>
  <div>
    <h1>Playlist Intelligence Agent</h1>
    <p>Frontend Placeholder - Vue.js + TypeScript + Vite</p>
    <a :href="loginUrl" class="login-button">Connect to Spotify</a>
  </div>
</template>

<style scoped>
  div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    font-family: sans-serif;
    background-color: #2c3e50;
    color: #ecf0f1;
    text-align: center;
  }
  h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  .login-button {
      display: inline-block;
      margin-top: 2rem;
      padding: 1rem 2rem;
      background-color: #1DB954; /* Spotify Green */
      color: white;
      border-radius: 50px;
      text-decoration: none;
      font-weight: bold;
      font-size: 1.2rem;
      transition: background-color 0.3s ease;
  }
  .login-button:hover {
      background-color: #1ED760;
  }
</style>
