[project]
name = "playlist-intelligence-agent-backend"
version = "0.1.0"
description = "Backend for the Playlist Intelligence Agent"
requires-python = ">=3.12.0"
dependencies = [
    "fastapi==0.115.12",
    "uvicorn[standard]==0.32.1",
    "pydantic==2.10.4",
    "pydantic-settings==2.7.1",
    "python-json-logger==2.0.7",
    "sqlalchemy==2.0.36",
    "alembic==1.14.0",
    "asyncpg==0.30.0",
    "psycopg2-binary==2.9.10",
    "spotipy==2.24.0",
    "python-jose[cryptography]==3.3.0",
    "cryptography==44.0.0",
    "ruff>=0.11.12",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.2.0",
    "pytest-cov>=5.0.0",
    "pytest-asyncio>=0.23.0",
    "httpx>=0.27.0",
    "pytest-mock>=3.12.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/app"]

[tool.uv]
# Configuration for uv can be added here if needed

[tool.alembic]
# This section can be used to configure alembic, but we'll use alembic.ini
# script_location = "backend/alembic" # Example

[tool.ruff]
line-length = 88
lint.select = ["E", "F", "W", "I", "UP"] # select common rule sets
lint.ignore = ["E501"] # ignore specific rules

[tool.ruff.format]
quote-style = "double"

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "live_api_test: marks tests that require live API access",
]

[tool.coverage.run]
source = ["src/app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/alembic/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[dependency-groups]
dev = [
    "httpx>=0.28.1",
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.1.1",
    "pytest-mock>=3.14.1",
]
