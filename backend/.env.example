# Backend Configuration Example
APP_TITLE="Playlist Intelligence Agent MVP - Dev"
API_VERSION="0.1.0"
LOG_LEVEL="INFO" # e.g., DEBUG, INFO, WARNING, ERROR
FRONTEND_URL="http://localhost:5173" # Default Vite dev port

DB_USER=admin
DB_PASSWORD=password
DB_HOST=localhost # Use 'db' if running backend *inside* docker, 'localhost' if outside
DB_PORT=5432
DB_NAME=playlist_agent_dev
DB_ECHO=false # Set to true to see SQLAlchemy SQL logs

SPOTIFY_CLIENT_ID="your_spotify_client_id_here"
SPOTIFY_CLIENT_SECRET="your_spotify_client_secret_here"
SPOTIFY_REDIRECT_URI="http://127.0.0.1:8000/api/v1/auth/callback"

# Generate using: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
TOKENS_ENCRYPTION_KEY="your_fernet_encryption_key_here"
# Generate using: python -c "import secrets; print(secrets.token_hex(32))"
JWT_SECRET_KEY="your_jwt_secret_key_here"
JWT_ALGORITHM="HS256"
JWT_EXPIRY_MINUTES=60 # Example: 1 hour
APP_ENV="development" # or "production" - used for cookie settings
