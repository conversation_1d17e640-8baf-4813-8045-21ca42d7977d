#!/usr/bin/env python3
"""
Test runner script for the backend application.
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(command: list[str], description: str) -> bool:
    """Run a command and return True if successful."""
    print(f"\n{'=' * 60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'=' * 60}")

    try:
        result = subprocess.run(command, check=True, capture_output=False)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"❌ Command not found: {command[0]}")
        return False


def main():
    """Main test runner function."""
    # Change to the backend directory
    backend_dir = Path(__file__).parent.parent
    print(f"Running tests from: {backend_dir}")

    # Commands to run
    commands = [
        {"command": ["uv", "sync", "--dev"], "description": "Installing dependencies"},
        {
            "command": ["uv", "run", "pytest", "tests/", "-v", "--tb=short"],
            "description": "Running all tests",
        },
        {
            "command": [
                "uv",
                "run",
                "pytest",
                "tests/unit/",
                "-v",
                "--tb=short",
                "-m",
                "not integration",
            ],
            "description": "Running unit tests only",
        },
        {
            "command": [
                "uv",
                "run",
                "pytest",
                "tests/integration/",
                "-v",
                "--tb=short",
                "-m",
                "integration",
            ],
            "description": "Running integration tests only",
        },
        {
            "command": [
                "uv",
                "run",
                "pytest",
                "tests/",
                "--cov=src/app",
                "--cov-report=term-missing",
                "--cov-report=html",
            ],
            "description": "Running tests with coverage",
        },
    ]

    # Track results
    results = []

    # Change to backend directory
    original_cwd = Path.cwd()
    try:
        os.chdir(backend_dir)

        for cmd_info in commands:
            success = run_command(cmd_info["command"], cmd_info["description"])
            results.append((cmd_info["description"], success))

    finally:
        os.chdir(original_cwd)

    # Print summary
    print(f"\n{'=' * 60}")
    print("TEST SUMMARY")
    print(f"{'=' * 60}")

    all_passed = True
    for description, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {description}")
        if not success:
            all_passed = False

    print(f"\n{'=' * 60}")
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print("💥 SOME TESTS FAILED!")
        sys.exit(1)


if __name__ == "__main__":
    main()
