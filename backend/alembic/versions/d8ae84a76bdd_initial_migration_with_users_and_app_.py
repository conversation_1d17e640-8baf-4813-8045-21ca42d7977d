"""initial_migration_with_users_and_app_settings

Revision ID: d8ae84a76bdd
Revises:
Create Date: 2025-05-31 17:23:22.297584

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

revision: str = "d8ae84a76bdd"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Ensure pgvector extension is created for future vector operations
    op.execute("CREATE EXTENSION IF NOT EXISTS vector;")

    op.create_table(
        "app_settings_stub",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("setting_name", sa.String(length=50), nullable=False),
        sa.Column("setting_value", sa.String(length=255), nullable=True),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("setting_name"),
    )
    op.create_index(
        op.f("ix_app_settings_stub_id"), "app_settings_stub", ["id"], unique=False
    )
    op.create_table(
        "users",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("spotify_user_id", sa.String(), nullable=False),
        sa.Column("spotify_display_name", sa.String(), nullable=True),
        sa.Column("access_token_encrypted", sa.LargeBinary(), nullable=False),
        sa.Column("refresh_token_encrypted", sa.LargeBinary(), nullable=False),
        sa.Column("expires_at", sa.TIMESTAMP(timezone=True), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_users_id"), "users", ["id"], unique=False)
    op.create_index(
        op.f("ix_users_spotify_user_id"), "users", ["spotify_user_id"], unique=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_users_spotify_user_id"), table_name="users")
    op.drop_index(op.f("ix_users_id"), table_name="users")
    op.drop_table("users")
    op.drop_index(op.f("ix_app_settings_stub_id"), table_name="app_settings_stub")
    op.drop_table("app_settings_stub")

    # Optionally drop the pgvector extension, but often you want to keep it
    # op.execute("DROP EXTENSION IF EXISTS vector;")
    # ### end Alembic commands ###
