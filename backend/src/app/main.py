import logging
import time
from contextlib import asynccontextmanager

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from app.api import auth, health, playlists_router
from app.config import settings
from app.core.exceptions import AppException
from app.core.security_config import (
    get_safe_headers,
    log_security_event,
    should_expose_detailed_errors,
)
from app.logging_config import (
    create_log_context,
    generate_correlation_id,
    set_correlation_id,
    setup_logging,
)

setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    logger.info(
        "Application startup commencing.",
        extra={"app_title": settings.APP_TITLE, "api_version": settings.API_VERSION},
    )
    yield
    logger.info("Application shutdown initiated.")


app = FastAPI(
    title=settings.APP_TITLE,
    version=settings.API_VERSION,
    description="API for managing Spotify playlists with AI-driven insights.",
    lifespan=lifespan,
)


@app.middleware("http")
async def security_headers_middleware(request: Request, call_next):
    """Middleware to add security headers and prevent information disclosure."""
    response = await call_next(request)

    del response.headers["server"]

    safe_headers = get_safe_headers()
    for header_name, header_value in safe_headers.items():
        response.headers[header_name] = header_value

    del response.headers["X-Debug-Info"]
    del response.headers["X-Internal-Error"]

    return response


@app.middleware("http")
async def correlation_and_logging_middleware(request: Request, call_next):
    """Middleware to set correlation ID and log requests with enhanced context."""
    correlation_id = generate_correlation_id()
    set_correlation_id(correlation_id)

    start_time = time.time()

    log_context = {
        "method": request.method,
        "path": request.url.path,
        "client_host": request.client.host if request.client else "unknown",
        "correlation_id": correlation_id,
    }

    logger.debug("Request started", extra=log_context)

    try:
        response = await call_next(request)
        process_time = (time.time() - start_time) * 1000

        response_context = {
            **log_context,
            "status_code": response.status_code,
            "process_time_ms": round(process_time, 2),
            "response_size": response.headers.get("content-length", "unknown"),
        }

        if response.status_code >= 500:
            logger.error("Request completed with server error", extra=response_context)
        elif response.status_code >= 400:
            logger.warning(
                "Request completed with client error", extra=response_context
            )
        else:
            logger.info("Request completed successfully", extra=response_context)

        return response

    except Exception as e:
        process_time = (time.time() - start_time) * 1000
        error_context = {
            **log_context,
            "process_time_ms": round(process_time, 2),
            "error_type": type(e).__name__,
        }
        logger.error(
            "Request failed with exception", extra=error_context, exc_info=True
        )
        raise


@app.exception_handler(AppException)
async def app_exception_handler(_request: Request, exc: AppException):
    """Handles custom application exceptions."""
    return JSONResponse(status_code=exc.status_code, content={"detail": exc.detail})


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handles Pydantic validation errors."""
    log_context = create_log_context(
        endpoint=request.url.path, operation="request_validation"
    )

    logger.warning(
        "Request validation failed",
        extra={
            **log_context,
            "validation_errors": exc.errors()
            if should_expose_detailed_errors()
            else "hidden",
            "error_count": len(exc.errors()),
        },
    )

    if should_expose_detailed_errors():
        return JSONResponse(
            status_code=422,
            content={"detail": "Validation Error", "errors": exc.errors()},
        )
    else:
        log_security_event(
            "validation_error", {"path": request.url.path}, request.url.path
        )
        return JSONResponse(
            status_code=422, content={"detail": "Invalid request data."}
        )


@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    """Handles any unhandled exceptions, returning a generic 500 response."""
    log_context = create_log_context(
        endpoint=request.url.path, operation="unhandled_exception"
    )

    logger.critical(
        "Unhandled exception occurred",
        extra={
            **log_context,
            "error_type": type(exc).__name__,
            "error_message": str(exc),
        },
        exc_info=True,
    )

    log_security_event(
        "unhandled_exception",
        {"error_type": type(exc).__name__, "path": request.url.path},
        request.url.path,
    )

    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected internal server error occurred."},
    )


app.include_router(health.router, prefix="/api/v1", tags=["System"])
app.include_router(auth.router, prefix="/api/v1", tags=["Auth"])
app.include_router(playlists_router.router, prefix="/api/v1", tags=["Playlists"])


@app.get("/", summary="Root", tags=["System"])
async def root():
    """Root endpoint providing basic info about the API."""
    return {
        "message": "Playlist Intelligence Agent API",
        "status": "operational",
        "docs_url": "/docs" if settings.APP_ENV == "development" else None,
    }
