import logging
from datetime import UTC, datetime, timedelta
from typing import TYPE_CHECKING, Any

import spotipy
from spotipy.oauth2 import SpotifyOAuth
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.core.exceptions import AppException, NotFoundException, UnauthorizedException
from app.core.models.dtos import (
    PlaylistTrackArtistDto,
    PlaylistTrackItemDto,
    PlaylistTracksDetailResponseDto,
    SpotifyPlaylistSummaryDto,
    UserPlaylistsResponseDto,
)
from app.core.models.user import User
from app.core.security import decrypt_token, encrypt_token
from app.logging_config import create_log_context
from app.repositories import user_repository

if TYPE_CHECKING:
    SpotifyTokenInfo = dict[str, Any]
    SpotifyPlaylistInfo = dict[str, Any]
    SpotifyPlaylistItems = dict[str, Any]
    SpotifyUserPlaylists = dict[str, Any]

logger = logging.getLogger(__name__)
SCOPES = "playlist-read-private playlist-read-collaborative playlist-modify-public playlist-modify-private"


def get_spotify_oauth() -> SpotifyOAuth:
    """Creates and returns a Spotipy OAuth manager instance."""
    if not all(
        [
            settings.SPOTIFY_CLIENT_ID,
            settings.SPOTIFY_CLIENT_SECRET,
            settings.SPOTIFY_REDIRECT_URI,
        ]
    ):
        msg = "Spotify credentials are not fully configured."
        logger.critical(msg)
        raise AppException(msg, 500)
    return SpotifyOAuth(
        client_id=settings.SPOTIFY_CLIENT_ID,
        client_secret=settings.SPOTIFY_CLIENT_SECRET,
        redirect_uri=settings.SPOTIFY_REDIRECT_URI,
        scope=SCOPES,
        cache_path=None,
        show_dialog=True,
    )


async def _refresh_and_get_token(db: AsyncSession, user: User) -> str:
    """Retrieves and refreshes a user's Spotify access token if necessary."""
    spotify_user_id: str = user.spotify_user_id  # type: ignore
    log_context = create_log_context(user_id=spotify_user_id, operation="token_refresh")

    current_time = datetime.now(UTC)
    expires_at_value: datetime = user.expires_at  # type: ignore

    if expires_at_value <= (current_time + timedelta(minutes=5)):
        logger.info(
            "Spotify token refresh required",
            extra={
                **log_context,
                "expires_at": expires_at_value.isoformat(),
                "time_until_expiry_minutes": (
                    expires_at_value - current_time
                ).total_seconds()
                / 60,
            },
        )

        sp_oauth = get_spotify_oauth()
        refresh_token_encrypted: bytes = user.refresh_token_encrypted  # type: ignore
        refresh_token = decrypt_token(refresh_token_encrypted)

        try:
            token_info: SpotifyTokenInfo = sp_oauth.refresh_access_token(refresh_token)
            if not token_info:
                logger.error(
                    "Spotify token refresh failed - no token info returned",
                    extra=log_context,
                )
                raise AppException("Failed to refresh Spotify token.", 500)

            user.access_token_encrypted = encrypt_token(token_info["access_token"])  # type: ignore
            if "refresh_token" in token_info:
                user.refresh_token_encrypted = encrypt_token(  # type: ignore
                    token_info["refresh_token"]
                )
            user.expires_at = datetime.fromtimestamp(  # type: ignore
                token_info["expires_at"], tz=UTC
            )

            await db.commit()
            await db.refresh(user)

            logger.info(
                "Spotify token refreshed successfully",
                extra={**log_context, "new_expires_at": user.expires_at.isoformat()},
            )
            return token_info["access_token"]

        except spotipy.SpotifyException as e:
            logger.error(
                "Spotify API token refresh failed",
                extra={
                    **log_context,
                    "error_type": type(e).__name__,
                    "spotify_error": str(e),
                },
                exc_info=True,
            )
            raise UnauthorizedException("Authentication expired. Please log in again.")
        except Exception as e:
            logger.error(
                "Token refresh operation failed",
                extra={
                    **log_context,
                    "error_type": type(e).__name__,
                    "error_message": str(e),
                },
                exc_info=True,
            )
            raise AppException(
                "Authentication service unavailable. Please try again.", 500
            )
    else:
        logger.debug(
            "Using existing valid token",
            extra={
                **log_context,
                "expires_at": expires_at_value.isoformat(),
                "time_until_expiry_minutes": (
                    expires_at_value - current_time
                ).total_seconds()
                / 60,
            },
        )
        access_token_encrypted: bytes = user.access_token_encrypted  # type: ignore
        return decrypt_token(access_token_encrypted)


async def get_spotify_client(db: AsyncSession, spotify_user_id: str) -> spotipy.Spotify:
    """Retrieves a Spotipy client for a user, handling token refresh if necessary."""
    user = await user_repository.get_user_by_spotify_id(db, spotify_user_id)
    if not user:
        raise UnauthorizedException("User not found or not authenticated.")
    access_token = await _refresh_and_get_token(db, user)
    return spotipy.Spotify(auth=access_token)


async def get_user_playlists(sp: spotipy.Spotify) -> UserPlaylistsResponseDto:
    """Fetches all playlists for an authenticated user."""
    log_context = create_log_context(operation="fetch_user_playlists")

    playlists = []
    offset = 0
    total_requests = 0

    try:
        logger.debug("Starting playlist fetch", extra=log_context)

        while True:
            total_requests += 1
            results: SpotifyUserPlaylists | None = sp.current_user_playlists(
                limit=50, offset=offset
            )
            if not results or not results["items"]:
                break

            batch_count = len(results["items"])
            for item in results["items"]:
                playlists.append(
                    SpotifyPlaylistSummaryDto(
                        id=item["id"],
                        name=item["name"],
                        track_count=item["tracks"]["total"],
                    )
                )

            logger.debug(
                "Playlist batch processed",
                extra={
                    **log_context,
                    "batch_size": batch_count,
                    "total_playlists": len(playlists),
                    "offset": offset,
                },
            )

            if results["next"]:
                offset += 50
            else:
                break

        logger.info(
            "User playlists retrieved successfully",
            extra={
                **log_context,
                "total_playlists": len(playlists),
                "api_requests": total_requests,
            },
        )
        return UserPlaylistsResponseDto(playlists=playlists)

    except spotipy.SpotifyException as e:
        logger.error(
            "Spotify API error fetching playlists",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "spotify_error": str(e),
                "playlists_fetched": len(playlists),
            },
            exc_info=True,
        )
        raise AppException(
            "Unable to fetch playlists from Spotify. Please try again.", 503
        )
    except Exception as e:
        logger.error(
            "Playlist fetch operation failed",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "playlists_fetched": len(playlists),
            },
            exc_info=True,
        )
        raise AppException("Unable to fetch playlists. Please try again.", 500)


async def get_playlist_tracks_details(
    sp: spotipy.Spotify, playlist_id: str
) -> PlaylistTracksDetailResponseDto:
    """Fetches details for a specific playlist."""
    log_context = create_log_context(
        operation="fetch_playlist_tracks", playlist_id=playlist_id
    )

    tracks = []
    offset = 0
    total_requests = 0

    try:
        logger.debug("Starting playlist tracks fetch", extra=log_context)

        playlist_info: SpotifyPlaylistInfo | None = sp.playlist(
            playlist_id, fields="snapshot_id,tracks.total"
        )
        if not playlist_info:
            logger.error("Failed to retrieve playlist metadata", extra=log_context)
            raise AppException("Failed to retrieve playlist information.", 500)

        snapshot_id = playlist_info["snapshot_id"]
        total_tracks = playlist_info["tracks"]["total"]

        enhanced_context = {
            **log_context,
            "snapshot_id": snapshot_id,
            "expected_tracks": total_tracks,
        }

        logger.info("Playlist metadata retrieved", extra=enhanced_context)

        while True:
            total_requests += 1
            results: SpotifyPlaylistItems | None = sp.playlist_items(
                playlist_id,
                fields="items(track(id,name,artists(id,name))),next",
                limit=100,
                offset=offset,
            )
            if not results or not results["items"]:
                break

            batch_count = 0
            for item in results["items"]:
                track = item.get("track")
                if track and track.get("id"):
                    artists = [
                        PlaylistTrackArtistDto(id=a.get("id"), name=a["name"])
                        for a in track.get("artists", [])
                    ]
                    tracks.append(
                        PlaylistTrackItemDto(
                            spotify_track_id=track["id"],
                            name=track["name"],
                            artists=artists,
                        )
                    )
                    batch_count += 1

            logger.debug(
                "Track batch processed",
                extra={
                    **enhanced_context,
                    "batch_size": batch_count,
                    "total_tracks_fetched": len(tracks),
                    "offset": offset,
                },
            )

            if results["next"]:
                offset += 100
            else:
                break

        logger.info(
            "Playlist tracks retrieved successfully",
            extra={
                **enhanced_context,
                "tracks_fetched": len(tracks),
                "api_requests": total_requests,
                "fetch_completion_rate": f"{len(tracks)}/{total_tracks}",
            },
        )

        return PlaylistTracksDetailResponseDto(
            snapshot_id=snapshot_id, tracks=tracks, total_tracks=total_tracks
        )

    except spotipy.SpotifyException as e:
        logger.error(
            "Spotify API error fetching playlist tracks",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "spotify_error": str(e),
                "tracks_fetched": len(tracks),
                "http_status": getattr(e, "http_status", "unknown"),
            },
            exc_info=True,
        )

        if getattr(e, "http_status", None) == 404:
            raise NotFoundException("Playlist not found.")
        raise AppException(
            "Unable to fetch playlist details from Spotify. Please try again.", 503
        )
    except Exception as e:
        logger.error(
            "Playlist tracks fetch operation failed",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "tracks_fetched": len(tracks),
            },
            exc_info=True,
        )
        raise AppException("Unable to fetch playlist details. Please try again.", 500)
