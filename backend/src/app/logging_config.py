import logging
import sys
import uuid
from contextvars import <PERSON>textV<PERSON>
from typing import Any

from pythonjsonlogger import jsonlogger

from app.config import settings

correlation_id_var: ContextVar[str | None] = ContextVar(
    "correlation_id", default=None
)


class CorrelationIdFilter(logging.Filter):
    """Filter to add correlation ID to log records."""

    def filter(self, record):
        correlation_id = correlation_id_var.get()
        record.correlation_id = correlation_id or "no-correlation"
        return True


class EnhancedJsonFormatter(jsonlogger.JsonFormatter):
    """Enhanced JSON formatter with additional structured fields."""

    def add_fields(self, log_record, record, message_dict):
        super().add_fields(log_record, record, message_dict)

        log_record["service"] = "playlist-intelligence-agent"
        log_record["environment"] = settings.APP_ENV

        if not hasattr(record, "correlation_id"):
            record.correlation_id = "no-correlation"


def setup_logging():
    """
    Configures enhanced structured logging for the application.
    """
    log_level = settings.get_log_level()
    logger = logging.getLogger()
    logger.setLevel(log_level)

    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    handler = logging.StreamHandler(sys.stdout)

    formatter = EnhancedJsonFormatter(
        "%(asctime)s %(levelname)s %(name)s %(correlation_id)s %(message)s"
    )
    handler.setFormatter(formatter)

    correlation_filter = CorrelationIdFilter()
    handler.addFilter(correlation_filter)

    logger.addHandler(handler)

    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.error").propagate = True
    logging.getLogger("fastapi").propagate = True

    if settings.APP_ENV == "production":
        logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
        logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)

    logging.getLogger(__name__).info(
        "Enhanced structured JSON logging configured.",
        extra={
            "log_level": settings.LOG_LEVEL,
            "environment": settings.APP_ENV,
            "service": "playlist-intelligence-agent",
        },
    )


def get_correlation_id() -> str | None:
    """Get the current correlation ID."""
    return correlation_id_var.get()


def set_correlation_id(correlation_id: str) -> None:
    """Set the correlation ID for the current context."""
    correlation_id_var.set(correlation_id)


def generate_correlation_id() -> str:
    """Generate a new correlation ID."""
    return str(uuid.uuid4())[:8]


def create_log_context(
    user_id: str | None = None,
    endpoint: str | None = None,
    operation: str | None = None,
    **kwargs,
) -> dict[str, Any]:
    """
    Create a standardized log context with common fields.

    Args:
        user_id: Spotify user ID (when available)
        endpoint: API endpoint being accessed
        operation: Business operation being performed
        **kwargs: Additional context fields

    Returns:
        Dictionary of log context fields
    """
    context = {
        "correlation_id": get_correlation_id(),
        "environment": settings.APP_ENV,
        "service": "playlist-intelligence-agent",
    }

    if user_id:
        context["user_id"] = user_id
    if endpoint:
        context["endpoint"] = endpoint
    if operation:
        context["operation"] = operation

    context.update(kwargs)

    return context
