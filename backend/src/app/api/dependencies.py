import logging

import spot<PERSON>y
from fastapi import Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import UnauthorizedException
from app.core.security import verify_app_jwt
from app.db.session import get_db
from app.integrations import spotify_service
from app.logging_config import create_log_context

logger = logging.getLogger(__name__)


async def get_current_spotify_user_id(request: Request) -> str:
    """
    Dependency to get the Spotify User ID from the JWT token.
    """
    log_context = create_log_context(
        endpoint=request.url.path, operation="jwt_authentication"
    )

    token = request.cookies.get("pia_session_token")
    if not token:
        logger.warning(
            "Authentication attempted without session token", extra=log_context
        )
        raise UnauthorizedException("Not authenticated.")

    try:
        payload = verify_app_jwt(token)
        spotify_user_id = payload.get("sub")
        if spotify_user_id is None:
            logger.warning("Invalid JWT token payload", extra=log_context)
            raise UnauthorizedException("Invalid token payload.")

        user_context = create_log_context(
            user_id=spotify_user_id,
            endpoint=request.url.path,
            operation="jwt_authentication",
        )
        logger.debug("User authenticated successfully", extra=user_context)
        return spotify_user_id

    except UnauthorizedException as e:
        logger.warning(
            "JWT authentication failed", extra={**log_context, "auth_error": e.detail}
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_spotify_client_dependency(
    spotify_user_id: str = Depends(get_current_spotify_user_id),
    db: AsyncSession = Depends(get_db),
) -> spotipy.Spotify:
    """
    FastAPI dependency to get a fully authenticated and ready-to-use Spotipy client.
    """
    log_context = create_log_context(
        user_id=spotify_user_id, operation="spotify_client_setup"
    )

    try:
        logger.debug("Setting up Spotify client", extra=log_context)
        return await spotify_service.get_spotify_client(db, spotify_user_id)
    except UnauthorizedException as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=e.detail)
    except Exception as e:
        logger.error(
            "Spotify client setup failed",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "error_message": str(e),
            },
            exc_info=True,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Service temporarily unavailable.",
        )
