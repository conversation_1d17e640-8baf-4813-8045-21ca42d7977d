import logging

from fastapi import APIRouter, Depends
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import AppException
from app.db.session import get_db
from app.logging_config import create_log_context

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/health", summary="API Health Check", tags=["System"])
async def health_check():
    """
    Basic health check endpoint.
    Returns a simple success response to indicate the API is up and running.
    """
    logger.debug(
        "Basic health check requested",
        extra=create_log_context(endpoint="/health", operation="health_check"),
    )
    return {"status": "ok"}


@router.get("/health/db", summary="Database Health Check", tags=["System"])
async def db_health_check(db: AsyncSession = Depends(get_db)):
    """
    Database health check endpoint.
    Tries to execute a simple query to ensure DB connection and pgvector is working.
    """
    log_context = create_log_context(
        endpoint="/health/db", operation="database_health_check"
    )

    logger.info("Database health check initiated", extra=log_context)

    try:
        result = await db.execute(text("SELECT 1;"))
        row = result.fetchone()

        if not row or row[0] != 1:
            logger.error(
                "Database connectivity test failed",
                extra={**log_context, "test_result": row[0] if row else None},
            )
            raise AppException("Database health check failed.", 500)

        await db.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))

        logger.info(
            "Database health check passed",
            extra={**log_context, "connectivity": "ok", "pgvector": "available"},
        )

        return {"status": "ok", "db_connection": "successful"}

    except Exception as e:
        logger.error(
            "Database health check failed",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "error_message": str(e),
            },
            exc_info=True,
        )
        raise AppException("Database service unavailable.", 503)


@router.get("/error-test", summary="Test Exception Handling", tags=["System"])
async def error_test():
    """
    Test endpoint to trigger an exception.
    """
    log_context = create_log_context(endpoint="/error-test", operation="exception_test")

    logger.warning(
        "Triggering test exception for error handling validation", extra=log_context
    )
    raise ValueError("This is a test error to check the 500 handler.")
