import logging

import spotipy
from fastapi import APIRouter, Depends, Path

from app.api.dependencies import get_spotify_client_dependency
from app.core.models.dtos import (
    PlaylistTracksDetailResponseDto,
    UserPlaylistsResponseDto,
)
from app.integrations import spotify_service
from app.logging_config import create_log_context

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/playlists", response_model=UserPlaylistsResponseDto, summary="Get User's Playlists", tags=["Playlists"])
async def list_user_playlists(sp: spotipy.Spotify = Depends(get_spotify_client_dependency)):
    """
    Retrieves a list of playlists for the currently authenticated user.
    """
    log_context = create_log_context(
        endpoint="/playlists",
        operation="api_list_playlists"
    )

    logger.info("User playlists API request", extra=log_context)
    return await spotify_service.get_user_playlists(sp)

@router.get("/playlists/{playlist_id}/details", response_model=PlaylistTracksDetailResponseDto, summary="Get Playlist Details & Tracks", tags=["Playlists"])
async def get_playlist_details(
    playlist_id: str = Path(..., description="The Spotify ID of the playlist."),
    sp: spotipy.Spotify = Depends(get_spotify_client_dependency)
):
    """
    Retrieves the snapshot ID and all tracks for a specific playlist.
    """
    log_context = create_log_context(
        endpoint=f"/playlists/{playlist_id}/details",
        operation="api_get_playlist_details",
        playlist_id=playlist_id
    )

    logger.info("Playlist details API request", extra=log_context)
    return await spotify_service.get_playlist_tracks_details(sp, playlist_id)
