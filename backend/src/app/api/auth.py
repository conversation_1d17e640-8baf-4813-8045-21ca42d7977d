import logging
from datetime import UTC, datetime

import spotipy
from fastapi import APIRouter, Depends, Query
from fastapi.responses import RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.core import security
from app.core.exceptions import AppException, BadRequestException
from app.db.session import get_db
from app.integrations.spotify_service import get_spotify_oauth
from app.logging_config import create_log_context
from app.repositories import user_repository

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/auth/login", summary="Initiate Spotify Login", tags=["Auth"])
async def spotify_login():
    """Redirects the user to Spotify for authentication."""
    log_context = create_log_context(
        endpoint="/auth/login", operation="spotify_auth_initiate"
    )

    sp_oauth = get_spotify_oauth()
    auth_url = sp_oauth.get_authorize_url()

    logger.info("Spotify authentication initiated", extra=log_context)
    return RedirectResponse(url=auth_url)


@router.get("/auth/callback", summary="Spotify Auth Callback", tags=["Auth"])
async def spotify_callback(code: str = Query(...), db: AsyncSession = Depends(get_db)):
    """Handles the callback from Spotify after user authorization."""
    log_context = create_log_context(
        endpoint="/auth/callback", operation="spotify_auth_callback"
    )

    logger.info("Spotify callback received", extra=log_context)
    sp_oauth = get_spotify_oauth()

    try:
        token_info = sp_oauth.get_access_token(code, check_cache=False)
        if not token_info:
            logger.error(
                "Failed to retrieve access token from Spotify", extra=log_context
            )
            raise BadRequestException("Failed to retrieve access token.")

        access_token = token_info["access_token"]
        refresh_token = token_info["refresh_token"]
        expires_at_dt = datetime.fromtimestamp(token_info["expires_at"], tz=UTC)

        sp = spotipy.Spotify(auth=access_token)
        spotify_user = sp.current_user()
        spotify_id = spotify_user["id"]  # type: ignore
        display_name = spotify_user["display_name"]  # type: ignore

        user_log_context = create_log_context(
            user_id=spotify_id,
            endpoint="/auth/callback",
            operation="spotify_auth_callback",
        )

        logger.info(
            "Spotify user profile retrieved",
            extra={**user_log_context, "display_name": display_name},
        )

        user = await user_repository.create_or_update_user(
            db=db,
            spotify_id=spotify_id,
            display_name=display_name,
            access_token=access_token,
            refresh_token=refresh_token,
            expires_at=expires_at_dt,
        )

        logger.info(
            "User authentication completed",
            extra={**user_log_context, "user_action": "created_or_updated"},
        )

        jwt_token = security.create_app_jwt(data={"sub": str(user.spotify_user_id)})

        redirect_url = f"{settings.FRONTEND_URL}/dashboard"
        response = RedirectResponse(url=redirect_url)
        response.set_cookie(
            key="pia_session_token",
            value=jwt_token,
            httponly=True,
            max_age=settings.JWT_EXPIRY_MINUTES * 60,
            expires=settings.JWT_EXPIRY_MINUTES * 60,
            path="/",
            domain=None,
            secure=settings.APP_ENV == "production",
            samesite="lax",
        )

        logger.info(
            "Authentication successful, redirecting to frontend",
            extra={**user_log_context, "redirect_url": redirect_url},
        )
        return response

    except spotipy.SpotifyException as e:
        logger.error(
            "Spotify API error during authentication",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "spotify_error": str(e),
            },
            exc_info=True,
        )
        raise AppException("Authentication with Spotify failed. Please try again.", 400)
    except Exception as e:
        logger.error(
            "Authentication callback failed",
            extra={
                **log_context,
                "error_type": type(e).__name__,
                "error_message": str(e),
            },
            exc_info=True,
        )
        raise AppException("Authentication failed. Please try again.", 500)
