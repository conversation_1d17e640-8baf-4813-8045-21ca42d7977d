import logging
from datetime import UTC, datetime, timedelta

from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from jose import ExpiredSignatureError, JWTError, jwt

from app.config import settings
from app.core.exceptions import UnauthorizedException

logger = logging.getLogger(__name__)

if not settings.TOKENS_ENCRYPTION_KEY or not settings.JWT_SECRET_KEY:
    raise ValueError("TOKENS_ENCRYPTION_KEY and JWT_SECRET_KEY must be set.")

_fernet = Fernet(settings.TOKENS_ENCRYPTION_KEY.encode())


def encrypt_token(token: str) -> bytes:
    """
    Encrypts a token using Fernet symmetric encryption.
    """
    try:
        return _fernet.encrypt(token.encode())
    except Exception as e:
        logger.error("Failed to encrypt token.", exc_info=True)
        raise e


def decrypt_token(encrypted_token: bytes) -> str:
    """
    Decrypts an encrypted token using Fernet symmetric encryption.
    """
    try:
        return _fernet.decrypt(encrypted_token).decode()
    except InvalidToken:
        logger.error("Failed to decrypt token: Invalid token.")
        raise UnauthorizedException("Invalid encrypted token data.")
    except Exception as e:
        logger.error("Failed to decrypt token.", exc_info=True)
        raise e


def create_app_jwt(data: dict) -> str:
    """Creates an application-specific JWT."""
    to_encode = data.copy()
    expire = datetime.now(UTC) + timedelta(minutes=settings.JWT_EXPIRY_MINUTES)
    to_encode.update({"exp": expire, "iat": datetime.now(UTC)})
    try:
        return jwt.encode(
            to_encode, str(settings.JWT_SECRET_KEY), algorithm=settings.JWT_ALGORITHM
        )
    except JWTError as e:
        logger.error("Failed to create JWT.", exc_info=True)
        raise e


def verify_app_jwt(token: str) -> dict:
    """Verifies an application-specific JWT."""
    try:
        return jwt.decode(
            token, str(settings.JWT_SECRET_KEY), algorithms=[settings.JWT_ALGORITHM]
        )
    except ExpiredSignatureError:
        logger.warning("JWT validation failed: Expired signature.")
        raise UnauthorizedException("Token has expired.")
    except JWTError as e:
        logger.error(f"JWT validation failed: {e}")
        raise UnauthorizedException("Could not validate credentials.")
