from pydantic import BaseModel, Field


class SpotifyPlaylistSummaryDto(BaseModel):
    """Summary of a Spotify playlist."""

    id: str = Field(..., description="Spotify Playlist ID")
    name: str = Field(..., description="Playlist Name")
    track_count: int = Field(..., description="Total number of tracks")


class UserPlaylistsResponseDto(BaseModel):
    """Response containing a list of user's Spotify playlists."""

    playlists: list[SpotifyPlaylistSummaryDto]


class PlaylistTrackArtistDto(BaseModel):
    """Details of an artist associated with a track."""

    id: str | None = Field(None, description="Spotify Artist ID")
    name: str = Field(..., description="Artist Name")


class PlaylistTrackItemDto(BaseModel):
    """Details of a track within a playlist."""

    spotify_track_id: str = Field(..., description="Spotify Track ID")
    name: str = Field(..., description="Track Name")
    artists: list[PlaylistTrackArtistDto] = Field(..., description="List of artists")


class PlaylistTracksDetailResponseDto(BaseModel):
    """Detailed response for a specific playlist's tracks."""

    snapshot_id: str = Field(..., description="Spotify Playlist Snapshot ID")
    tracks: list[PlaylistTrackItemDto] = Field(..., description="List of tracks")
    total_tracks: int = Field(
        ..., description="Total number of tracks reported by Spotify"
    )


class ErrorResponseDto(BaseModel):
    """Standardized error response format."""

    detail: str = Field(..., description="Error message")
    error_code: str | None = Field(None, description="Application-specific error code")

    model_config = {"extra": "forbid"}
