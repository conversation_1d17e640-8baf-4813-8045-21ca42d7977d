import sqlalchemy as sa
from sqlalchemy.sql import func

from app.db.session import Base


class User(Base):
    __tablename__ = 'users'
    id = sa.Column(sa.Integer, primary_key=True, index=True)
    spotify_user_id = sa.Column(sa.String, unique=True, index=True, nullable=False)
    spotify_display_name = sa.Column(sa.String, nullable=True)
    access_token_encrypted = sa.Column(sa.LargeBinary, nullable=False)
    refresh_token_encrypted = sa.Column(sa.LargeBinary, nullable=False)
    expires_at = sa.Column(sa.TIMESTAMP(timezone=True), nullable=False)
    created_at = sa.Column(sa.TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)
    updated_at = sa.Column(sa.TIMESTAMP(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
        return f"<User(id={self.id}, spotify_id='{self.spotify_user_id}')>"
