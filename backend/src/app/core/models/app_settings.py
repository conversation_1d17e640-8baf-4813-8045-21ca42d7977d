import sqlalchemy as sa
from sqlalchemy.sql import func

from app.db.session import Base


class AppSettingsStub(Base):
    """
    Placeholder model for initial migration to ensure Alembic works.
    """
    __tablename__ = 'app_settings_stub'

    id = sa.Column(sa.Integer, primary_key=True, index=True)
    setting_name = sa.Column(sa.String(50), nullable=False, unique=True)
    setting_value = sa.Column(sa.String(255), nullable=True)
    created_at = sa.Column(sa.TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)
    updated_at = sa.Column(sa.TIMESTAMP(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
            return f"<AppSettingsStub(id={self.id}, name='{self.setting_name}')>"
