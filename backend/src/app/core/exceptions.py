from fastapi import status


class AppException(Exception):
    """Base exception for the application."""
    def __init__(self, detail: str, status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR):
        self.detail = detail
        self.status_code = status_code
        super().__init__(detail)

class BadRequestException(AppException):
    """Raised when the request is invalid."""
    def __init__(self, detail: str = "Bad Request"):
        super().__init__(detail, status.HTTP_400_BAD_REQUEST)

class UnauthorizedException(AppException):
    """Raised when the user is not authenticated."""
    def __init__(self, detail: str = "Unauthorized"):
        super().__init__(detail, status.HTTP_401_UNAUTHORIZED)

class ForbiddenException(AppException):
    """Raised when the user is not authorized to access the resource."""
    def __init__(self, detail: str = "Forbidden"):
        super().__init__(detail, status.HTTP_403_FORBIDDEN)

class NotFoundException(AppException):
    """Raised when the requested resource is not found."""
    def __init__(self, detail: str = "Not Found"):
        super().__init__(detail, status.HTTP_404_NOT_FOUND)
