"""
Security configuration and utilities for the application.
This module centralizes security-related settings and functions to prevent information disclosure.
"""

import logging
from typing import Any

from app.config import settings

logger = logging.getLogger(__name__)

GENERIC_ERROR_MESSAGES = {
    "auth_failed": "Authentication failed. Please try again.",
    "access_denied": "Access denied.",
    "invalid_request": "Invalid request data.",
    "service_unavailable": "Service temporarily unavailable.",
    "resource_not_found": "Resource not found.",
    "spotify_error": "Unable to connect to Spotify. Please try again.",
    "database_error": "Database service unavailable.",
    "internal_error": "An unexpected error occurred. Please try again.",
}


def sanitize_error_message(
    error_type: str, original_message: str | None = None
) -> str:
    """
    Returns a sanitized error message that doesn't expose internal details.

    Args:
        error_type: Type of error (key from GENERIC_ERROR_MESSAGES)
        original_message: Original error message (logged but not returned to client)

    Returns:
        Sanitized error message safe for client consumption
    """
    if original_message:
        logger.error(f"Original error message: {original_message}")

    return GENERIC_ERROR_MESSAGES.get(
        error_type, GENERIC_ERROR_MESSAGES["internal_error"]
    )


def sanitize_response_data(data: dict[str, Any]) -> dict[str, Any]:
    """
    Removes sensitive fields from response data.

    Args:
        data: Response data dictionary

    Returns:
        Sanitized response data
    """
    sensitive_fields = {
        "password",
        "secret",
        "key",
        "token",
        "credential",
        "internal_id",
        "db_",
        "sql_",
        "error_trace",
        "stack_trace",
        "file_path",
        "server_",
        "host_",
        "port_",
        "connection_string",
    }

    sanitized = {}
    for key, value in data.items():
        if any(sensitive in key.lower() for sensitive in sensitive_fields):
            logger.warning(f"Sensitive field '{key}' excluded from response")
            continue

        if isinstance(value, dict):
            sanitized[key] = sanitize_response_data(value)
        elif isinstance(value, list):
            sanitized[key] = [
                sanitize_response_data(item) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            sanitized[key] = value

    return sanitized


def should_expose_detailed_errors() -> bool:
    """
    Determines if detailed error information should be exposed based on environment.

    Returns:
        True if detailed errors should be shown (development), False otherwise
    """
    return settings.APP_ENV == "development"


def get_safe_headers() -> dict[str, str]:
    """
    Returns security headers that should be added to all responses.

    Returns:
        Dictionary of security headers
    """
    return {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
    }


def log_security_event(
    event_type: str, details: dict[str, Any], request_path: str | None = None
):
    """
    Logs security-related events for monitoring and analysis.

    Args:
        event_type: Type of security event
        details: Event details (will be sanitized before logging)
        request_path: Request path if applicable
    """
    sanitized_details = sanitize_response_data(details)
    logger.warning(
        f"Security event: {event_type}",
        extra={
            "event_type": event_type,
            "details": sanitized_details,
            "request_path": request_path,
        },
    )
