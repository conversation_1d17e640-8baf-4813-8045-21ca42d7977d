import logging
from datetime import datetime

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.models.user import User
from app.core.security import encrypt_token

logger = logging.getLogger(__name__)

async def get_user_by_spotify_id(db: AsyncSession, spotify_id: str) -> User | None:
    """Retrieves a user by their Spotify ID."""
    result = await db.execute(select(User).where(User.spotify_user_id == spotify_id))
    return result.scalars().first()

async def create_or_update_user(
    db: AsyncSession, spotify_id: str, display_name: str,
    access_token: str, refresh_token: str, expires_at: datetime,
) -> User:
    """Creates a new user or updates an existing one."""
    user = await get_user_by_spotify_id(db, spotify_id)
    encrypted_access = encrypt_token(access_token)
    encrypted_refresh = encrypt_token(refresh_token)
    if user:
        logger.info(f"Updating user: {spotify_id}")
        user.spotify_display_name = display_name # type: ignore
        user.access_token_encrypted = encrypted_access # type: ignore
        user.refresh_token_encrypted = encrypted_refresh # type: ignore
        user.expires_at = expires_at # type: ignore
    else:
        logger.info(f"Creating new user: {spotify_id}")
        user = User(
            spotify_user_id=spotify_id,
            spotify_display_name=display_name,
            access_token_encrypted=encrypted_access,
            refresh_token_encrypted=encrypted_refresh,
            expires_at=expires_at,
        )
        db.add(user)
    await db.commit()
    await db.refresh(user)
    return user
