# Playlist Intelligence Agent - Backend

This directory contains the Python FastAPI backend for the Playlist Intelligence Agent MVP.

## Overview

The backend handles:
* Spotify Authentication & API Interaction
* Database Management (PostgreSQL + pgvector)
* "Playlist DNA" Calculation
* Recommendation/Removal Logic
* Serving the API for the frontend.

## Setup & Running Locally

**Prerequisites:** Python 3.12.0 or higher is required.

1.  **Start Database (using Docker):**
    * Ensure you have Docker and Docker Compose installed.
    * Create a `.env` file in the *project root* directory (or ensure `DB_USER`, `DB_PASSWORD`, etc., are set in your environment). You can copy `.env.example`.
    * From the *project root* directory, run:
        ```bash
        docker-compose up -d db
        ```
    * Wait a few moments for the database to initialize. You can check logs with `docker-compose logs db`.
    * To shutdown the database when done:
        ```bash
        docker-compose down
        ```

2.  **Navigate to Backend Directory:**
    ```bash
    cd backend
    ```

3.  **Create & Activate Virtual Environment (using `uv`):**
    ```bash
    uv venv --python 3.12
    source .venv/bin/activate  # On Windows: .venv\Scripts\activate
    ```

4.  **Install Dependencies:**
    ```bash
    uv pip install -e .
    ```

5.  **Create `.env` File:**
    * Copy `backend/.env.example` to `backend/.env`.
    * **Important:** Ensure `DB_HOST` is set to `localhost` (or `127.0.0.1`) since the app runs *outside* Docker, connecting to the exposed Docker port. Update other variables if you changed them in the root `.env` or `docker-compose.yml`.
    * **Crucial:** You *must* create a Spotify Developer Application at [developer.spotify.com](https://developer.spotify.com/dashboard).
        * Set `SPOTIFY_CLIENT_ID` and `SPOTIFY_CLIENT_SECRET` in `.env`.
        * Add `http://127.0.0.1:8000/api/v1/auth/callback` as a Redirect URI in your Spotify App settings.
    * **Crucial:** Generate `TOKENS_ENCRYPTION_KEY` and `JWT_SECRET_KEY` and add them to `.env`. Use the commands provided in `.env.example`.
    * Set `FRONTEND_URL` if your frontend runs on a different port than `5173`.

6.  **Run Database Migrations:**
    * Make sure you are in the `backend/` directory with the venv activated.
    * Run:
        ```bash
        alembic upgrade head
        ```

7.  **Run the Development Server:**
    * Make sure you are in the `backend/` directory with the venv activated.
        ```bash
        cd backend
        ```
    * Run Uvicorn:
        ```bash
        uv run uvicorn src.app.main:app --reload --host 0.0.0.0 --port 8000
        ```

8.  **Access the API:**
    * API: `http://localhost:8000`
    * Health: `http://localhost:8000/api/v1/health`
    * DB Health: `http://localhost:8000/api/v1/health/db`
    * Docs: `http://localhost:8000/docs`

## Project Structure

Refer to the main `docs/project-structure.md` for details. Key directories include:

* `src/app/`: Main application code.
    * `api/`: API Routers.
    * `core/`: Core models, exceptions, etc.
    * `services/`: Business logic.
    * `repositories/`: Data access.
    * `integrations/`: External API clients.
    * `config.py`: Configuration.
    * `main.py`: FastAPI entry point.
* `tests/`: Unit and integration tests.
* `alembic/`: Database migrations.
