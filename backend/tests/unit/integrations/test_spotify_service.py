"""
Unit tests for Spotify integration service.
"""

import os
from unittest.mock import patch

import pytest
from spotipy import SpotifyException

from app.core.exceptions import AppException, NotFoundException
from app.core.models.dtos import (
    PlaylistTrackItemDto,
    PlaylistTracksDetailResponseDto,
    SpotifyPlaylistSummaryDto,
    UserPlaylistsResponseDto,
)
from app.integrations.spotify_service import (
    get_playlist_tracks_details,
    get_spotify_oauth,
    get_user_playlists,
)


class TestSpotifyOAuth:
    """Test cases for Spotify OAuth functionality."""

    @pytest.fixture(autouse=True)
    def setup_spotify_credentials(self):
        """Set up Spotify credentials for testing."""
        with patch.dict(
            os.environ,
            {
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/api/v1/auth/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            yield

    def test_get_spotify_oauth_success(self):
        """Test successful creation of Spotify OAuth manager."""
        oauth = get_spotify_oauth()

        assert oauth is not None
        assert hasattr(oauth, "get_authorize_url")
        assert hasattr(oauth, "get_access_token")
        assert hasattr(oauth, "refresh_access_token")

    def test_get_spotify_oauth_missing_credentials(self):
        """Test that missing credentials raise AppException."""
        # Test missing client ID
        with patch("app.integrations.spotify_service.settings") as mock_settings:
            mock_settings.SPOTIFY_CLIENT_ID = None
            mock_settings.SPOTIFY_CLIENT_SECRET = "test_client_secret"
            mock_settings.SPOTIFY_REDIRECT_URI = "http://localhost:8000/callback"

            with pytest.raises(AppException) as exc_info:
                get_spotify_oauth()
            assert "Spotify credentials are not fully configured" in str(exc_info.value)

        # Test missing client secret
        with patch("app.integrations.spotify_service.settings") as mock_settings:
            mock_settings.SPOTIFY_CLIENT_ID = "test_client_id"
            mock_settings.SPOTIFY_CLIENT_SECRET = None
            mock_settings.SPOTIFY_REDIRECT_URI = "http://localhost:8000/callback"

            with pytest.raises(AppException) as exc_info:
                get_spotify_oauth()
            assert "Spotify credentials are not fully configured" in str(exc_info.value)


class TestGetUserPlaylists:
    """Test cases for getting user playlists."""

    async def test_get_user_playlists_success_single_page(
        self, mock_spotify_client, sample_spotify_playlist_response
    ):
        """Test successful retrieval of user playlists with single page."""
        # Setup mock response
        mock_spotify_client.current_user_playlists.return_value = (
            sample_spotify_playlist_response
        )

        result = await get_user_playlists(mock_spotify_client)

        assert isinstance(result, UserPlaylistsResponseDto)
        assert len(result.playlists) == 2

        # Check first playlist
        playlist1 = result.playlists[0]
        assert isinstance(playlist1, SpotifyPlaylistSummaryDto)
        assert playlist1.id == "playlist1"
        assert playlist1.name == "My Playlist 1"
        assert playlist1.track_count == 25

        # Check second playlist
        playlist2 = result.playlists[1]
        assert playlist2.id == "playlist2"
        assert playlist2.name == "My Playlist 2"
        assert playlist2.track_count == 50

        # Verify API was called correctly
        mock_spotify_client.current_user_playlists.assert_called_once_with(
            limit=50, offset=0
        )

    async def test_get_user_playlists_success_multiple_pages(self, mock_spotify_client):
        """Test successful retrieval of user playlists with pagination."""
        # Setup mock responses for pagination
        first_page = {
            "items": [
                {"id": "playlist1", "name": "Playlist 1", "tracks": {"total": 10}},
                {"id": "playlist2", "name": "Playlist 2", "tracks": {"total": 20}},
            ],
            "next": "https://api.spotify.com/v1/users/user/playlists?offset=50&limit=50",
        }

        second_page = {
            "items": [
                {"id": "playlist3", "name": "Playlist 3", "tracks": {"total": 30}}
            ],
            "next": None,
        }

        mock_spotify_client.current_user_playlists.side_effect = [
            first_page,
            second_page,
        ]

        result = await get_user_playlists(mock_spotify_client)

        assert len(result.playlists) == 3
        assert result.playlists[0].id == "playlist1"
        assert result.playlists[1].id == "playlist2"
        assert result.playlists[2].id == "playlist3"

        # Verify pagination calls
        assert mock_spotify_client.current_user_playlists.call_count == 2
        mock_spotify_client.current_user_playlists.assert_any_call(limit=50, offset=0)
        mock_spotify_client.current_user_playlists.assert_any_call(limit=50, offset=50)

    async def test_get_user_playlists_empty_response(self, mock_spotify_client):
        """Test handling of empty playlist response."""
        mock_spotify_client.current_user_playlists.return_value = {
            "items": [],
            "next": None,
        }

        result = await get_user_playlists(mock_spotify_client)

        assert isinstance(result, UserPlaylistsResponseDto)
        assert len(result.playlists) == 0

    async def test_get_user_playlists_spotify_exception(self, mock_spotify_client):
        """Test handling of Spotify API exceptions."""
        mock_spotify_client.current_user_playlists.side_effect = SpotifyException(
            http_status=429, code=-1, msg="Rate limit exceeded"
        )

        with pytest.raises(AppException) as exc_info:
            await get_user_playlists(mock_spotify_client)

        assert "Unable to fetch playlists from Spotify" in str(exc_info.value)

    async def test_get_user_playlists_none_response(self, mock_spotify_client):
        """Test handling of None response from Spotify."""
        mock_spotify_client.current_user_playlists.return_value = None

        result = await get_user_playlists(mock_spotify_client)

        assert isinstance(result, UserPlaylistsResponseDto)
        assert len(result.playlists) == 0


class TestGetPlaylistTracksDetails:
    """Test cases for getting playlist track details."""

    async def test_get_playlist_tracks_details_success(
        self,
        mock_spotify_client,
        sample_spotify_playlist_info,
        sample_spotify_playlist_tracks_response,
    ):
        """Test successful retrieval of playlist track details."""
        playlist_id = "test_playlist_123"

        # Setup mock responses
        mock_spotify_client.playlist.return_value = sample_spotify_playlist_info
        mock_spotify_client.playlist_items.return_value = (
            sample_spotify_playlist_tracks_response
        )

        result = await get_playlist_tracks_details(mock_spotify_client, playlist_id)

        assert isinstance(result, PlaylistTracksDetailResponseDto)
        assert result.snapshot_id == "snapshot123"
        assert result.total_tracks == 2
        assert len(result.tracks) == 2

        # Check first track
        track1 = result.tracks[0]
        assert isinstance(track1, PlaylistTrackItemDto)
        assert track1.spotify_track_id == "track1"
        assert track1.name == "Song 1"
        assert len(track1.artists) == 2
        assert track1.artists[0].id == "artist1"
        assert track1.artists[0].name == "Artist 1"

        # Check second track
        track2 = result.tracks[1]
        assert track2.spotify_track_id == "track2"
        assert track2.name == "Song 2"
        assert len(track2.artists) == 1
        assert track2.artists[0].id == "artist3"
        assert track2.artists[0].name == "Artist 3"

        # Verify API calls
        mock_spotify_client.playlist.assert_called_once_with(
            playlist_id, fields="snapshot_id,tracks.total"
        )
        mock_spotify_client.playlist_items.assert_called_once_with(
            playlist_id,
            fields="items(track(id,name,artists(id,name))),next",
            limit=100,
            offset=0,
        )

    async def test_get_playlist_tracks_details_pagination(
        self, mock_spotify_client, sample_spotify_playlist_info
    ):
        """Test playlist track details with pagination."""
        playlist_id = "test_playlist_123"

        # Setup mock responses for pagination
        first_page = {
            "items": [
                {
                    "track": {
                        "id": "track1",
                        "name": "Song 1",
                        "artists": [{"id": "artist1", "name": "Artist 1"}],
                    }
                }
            ],
            "next": "https://api.spotify.com/v1/playlists/playlist/tracks?offset=100&limit=100",
        }

        second_page = {
            "items": [
                {
                    "track": {
                        "id": "track2",
                        "name": "Song 2",
                        "artists": [{"id": "artist2", "name": "Artist 2"}],
                    }
                }
            ],
            "next": None,
        }

        mock_spotify_client.playlist.return_value = sample_spotify_playlist_info
        mock_spotify_client.playlist_items.side_effect = [first_page, second_page]

        result = await get_playlist_tracks_details(mock_spotify_client, playlist_id)

        assert len(result.tracks) == 2
        assert result.tracks[0].spotify_track_id == "track1"
        assert result.tracks[1].spotify_track_id == "track2"

        # Verify pagination calls
        assert mock_spotify_client.playlist_items.call_count == 2

    async def test_get_playlist_tracks_details_playlist_not_found(
        self, mock_spotify_client
    ):
        """Test handling of playlist not found error."""
        playlist_id = "nonexistent_playlist"

        mock_spotify_client.playlist.side_effect = SpotifyException(
            http_status=404, code=-1, msg="Playlist not found"
        )

        with pytest.raises(NotFoundException) as exc_info:
            await get_playlist_tracks_details(mock_spotify_client, playlist_id)

        assert "Playlist not found" in str(exc_info.value)

    async def test_get_playlist_tracks_details_tracks_not_found(
        self, mock_spotify_client, sample_spotify_playlist_info
    ):
        """Test handling of tracks not found error."""
        playlist_id = "test_playlist_123"

        mock_spotify_client.playlist.return_value = sample_spotify_playlist_info
        mock_spotify_client.playlist_items.side_effect = SpotifyException(
            http_status=404, code=-1, msg="Tracks not found"
        )

        with pytest.raises(NotFoundException) as exc_info:
            await get_playlist_tracks_details(mock_spotify_client, playlist_id)

        assert "Playlist not found" in str(exc_info.value)

    async def test_get_playlist_tracks_details_spotify_exception(
        self, mock_spotify_client, sample_spotify_playlist_info
    ):
        """Test handling of general Spotify API exceptions."""
        playlist_id = "test_playlist_123"

        mock_spotify_client.playlist.return_value = sample_spotify_playlist_info
        mock_spotify_client.playlist_items.side_effect = SpotifyException(
            http_status=500, code=-1, msg="Internal server error"
        )

        with pytest.raises(AppException) as exc_info:
            await get_playlist_tracks_details(mock_spotify_client, playlist_id)

        assert "Unable to fetch playlist details from Spotify" in str(exc_info.value)

    async def test_get_playlist_tracks_details_skip_invalid_tracks(
        self, mock_spotify_client, sample_spotify_playlist_info
    ):
        """Test that invalid tracks are skipped."""
        playlist_id = "test_playlist_123"

        # Response with some invalid tracks
        tracks_response = {
            "items": [
                {
                    "track": {
                        "id": "track1",
                        "name": "Valid Song",
                        "artists": [{"id": "artist1", "name": "Artist 1"}],
                    }
                },
                {
                    "track": None  # Invalid track
                },
                {
                    "track": {
                        "id": None,  # Invalid track ID
                        "name": "Invalid Song",
                        "artists": [],
                    }
                },
                {
                    "track": {
                        "id": "track2",
                        "name": "Another Valid Song",
                        "artists": [{"id": "artist2", "name": "Artist 2"}],
                    }
                },
            ],
            "next": None,
        }

        mock_spotify_client.playlist.return_value = sample_spotify_playlist_info
        mock_spotify_client.playlist_items.return_value = tracks_response

        result = await get_playlist_tracks_details(mock_spotify_client, playlist_id)

        # Should only include valid tracks
        assert len(result.tracks) == 2
        assert result.tracks[0].spotify_track_id == "track1"
        assert result.tracks[1].spotify_track_id == "track2"
