"""
Unit tests for security utilities including token encryption/decryption and JWT handling.
"""

import os
from unittest.mock import patch

import pytest
from cryptography.fernet import Fernet

from app.core.exceptions import UnauthorizedException
from app.core.security import (
    create_app_jwt,
    decrypt_token,
    encrypt_token,
    verify_app_jwt,
)


class TestTokenEncryption:
    """Test cases for token encryption and decryption."""

    @pytest.fixture(autouse=True)
    def setup_encryption_key(self):
        """Set up a valid encryption key for testing."""
        # Generate a valid Fernet key for testing
        test_key = Fernet.generate_key().decode()
        with patch.dict(
            os.environ,
            {
                "TOKENS_ENCRYPTION_KEY": test_key,
                "JWT_SECRET_KEY": "test_jwt_secret_key_for_testing",
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
            },
        ):
            # Re-import to pick up the new environment variables
            import importlib

            import app.core.security

            importlib.reload(app.core.security)
            yield

    def test_encrypt_token_success(self):
        """Test successful token encryption."""
        test_token = "test_access_token_12345"
        encrypted_token = encrypt_token(test_token)

        assert isinstance(encrypted_token, bytes)
        assert encrypted_token != test_token.encode()
        assert len(encrypted_token) > len(test_token)

    def test_decrypt_token_success(self):
        """Test successful token decryption."""
        test_token = "test_access_token_12345"
        encrypted_token = encrypt_token(test_token)
        decrypted_token = decrypt_token(encrypted_token)

        assert decrypted_token == test_token
        assert isinstance(decrypted_token, str)

    def test_encrypt_decrypt_roundtrip(self):
        """Test that encryption and decryption work together correctly."""
        original_tokens = [
            "short_token",
            "a_much_longer_token_with_special_characters_!@#$%^&*()",
            "spotify_access_token_BQC4YXNlcnRpb25faGVyZQ",
            "refresh_token_AQD8xJzKaXNlcnRpb25faGVyZQ",
        ]

        for original_token in original_tokens:
            encrypted = encrypt_token(original_token)
            decrypted = decrypt_token(encrypted)
            assert decrypted == original_token

    def test_decrypt_invalid_token(self):
        """Test that decrypting an invalid token raises UnauthorizedException."""
        invalid_encrypted_token = b"invalid_encrypted_data"

        with pytest.raises(UnauthorizedException) as exc_info:
            decrypt_token(invalid_encrypted_token)

        assert "Invalid encrypted token data" in str(exc_info.value)

    def test_encrypt_empty_string(self):
        """Test encrypting an empty string."""
        empty_token = ""
        encrypted_token = encrypt_token(empty_token)
        decrypted_token = decrypt_token(encrypted_token)

        assert decrypted_token == empty_token

    def test_encrypt_unicode_token(self):
        """Test encrypting a token with unicode characters."""
        unicode_token = "token_with_unicode_🎵🎶"
        encrypted_token = encrypt_token(unicode_token)
        decrypted_token = decrypt_token(encrypted_token)

        assert decrypted_token == unicode_token


class TestJWTHandling:
    """Test cases for JWT creation and verification."""

    @pytest.fixture(autouse=True)
    def setup_jwt_secret(self):
        """Set up JWT secret for testing."""
        with patch.dict(
            os.environ,
            {
                "JWT_SECRET_KEY": "test_jwt_secret_key_for_testing_purposes",
                "JWT_ALGORITHM": "HS256",
                "JWT_EXPIRY_MINUTES": "60",
                "TOKENS_ENCRYPTION_KEY": Fernet.generate_key().decode(),
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
            },
        ):
            # Re-import to pick up the new environment variables
            import importlib

            import app.core.security

            importlib.reload(app.core.security)
            yield

    def test_create_app_jwt_success(self):
        """Test successful JWT creation."""
        test_data = {"user_id": "test_user_123", "spotify_user_id": "spotify_user_456"}

        jwt_token = create_app_jwt(test_data)

        assert isinstance(jwt_token, str)
        assert len(jwt_token) > 0
        # JWT should have three parts separated by dots
        assert len(jwt_token.split(".")) == 3

    def test_verify_app_jwt_success(self):
        """Test successful JWT verification."""
        test_data = {"user_id": "test_user_123", "spotify_user_id": "spotify_user_456"}

        jwt_token = create_app_jwt(test_data)
        decoded_data = verify_app_jwt(jwt_token)

        assert decoded_data["user_id"] == test_data["user_id"]
        assert decoded_data["spotify_user_id"] == test_data["spotify_user_id"]
        assert "exp" in decoded_data  # Expiration should be added
        assert "iat" in decoded_data  # Issued at should be added

    def test_create_verify_jwt_roundtrip(self):
        """Test that JWT creation and verification work together."""
        test_cases = [
            {"user_id": "user1", "role": "admin"},
            {"spotify_user_id": "spotify123", "permissions": ["read", "write"]},
            {"user_id": "user2", "metadata": {"last_login": "2024-01-01"}},
        ]

        for test_data in test_cases:
            jwt_token = create_app_jwt(test_data)
            decoded_data = verify_app_jwt(jwt_token)

            # Check that all original data is preserved
            for key, value in test_data.items():
                assert decoded_data[key] == value

    def test_verify_invalid_jwt(self):
        """Test that verifying an invalid JWT raises UnauthorizedException."""
        invalid_jwt = "invalid.jwt.token"

        with pytest.raises(UnauthorizedException) as exc_info:
            verify_app_jwt(invalid_jwt)

        assert "Could not validate credentials" in str(exc_info.value)

    def test_verify_malformed_jwt(self):
        """Test that verifying a malformed JWT raises UnauthorizedException."""
        malformed_jwt = "not_a_jwt_at_all"

        with pytest.raises(UnauthorizedException) as exc_info:
            verify_app_jwt(malformed_jwt)

        assert "Could not validate credentials" in str(exc_info.value)

    def test_jwt_expiration_handling(self):
        """Test that expired JWTs are properly handled."""
        from datetime import UTC, datetime, timedelta

        from jose import jwt

        from app.config import settings

        # Create an expired JWT manually using the actual secret key
        test_data = {
            "user_id": "test_user",
            "exp": datetime.now(UTC) - timedelta(minutes=1),  # Expired 1 minute ago
            "iat": datetime.now(UTC) - timedelta(minutes=2),  # Issued 2 minutes ago
        }

        # Create the JWT with expired timestamp using the actual settings
        expired_jwt = jwt.encode(
            test_data, str(settings.JWT_SECRET_KEY), algorithm=settings.JWT_ALGORITHM
        )

        # Verify that it raises an exception
        with pytest.raises(UnauthorizedException) as exc_info:
            verify_app_jwt(expired_jwt)

        assert "Token has expired" in str(exc_info.value)

    def test_jwt_with_different_algorithm(self):
        """Test JWT creation and verification with different algorithm."""
        with patch.dict(os.environ, {"JWT_ALGORITHM": "HS512"}):
            import importlib

            import app.core.security

            importlib.reload(app.core.security)

            test_data = {"user_id": "test_user_algorithm"}
            jwt_token = create_app_jwt(test_data)
            decoded_data = verify_app_jwt(jwt_token)

            assert decoded_data["user_id"] == test_data["user_id"]

    def test_jwt_with_empty_data(self):
        """Test JWT creation with empty data."""
        empty_data = {}
        jwt_token = create_app_jwt(empty_data)
        decoded_data = verify_app_jwt(jwt_token)

        # Should still have exp and iat claims
        assert "exp" in decoded_data
        assert "iat" in decoded_data

    def test_jwt_with_none_values(self):
        """Test JWT creation with None values."""
        test_data = {"user_id": "test_user", "optional_field": None}

        jwt_token = create_app_jwt(test_data)
        decoded_data = verify_app_jwt(jwt_token)

        assert decoded_data["user_id"] == test_data["user_id"]
        assert decoded_data["optional_field"] is None
