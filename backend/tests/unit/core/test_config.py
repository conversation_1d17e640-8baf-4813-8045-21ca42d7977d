"""
Unit tests for configuration loading and validation.
"""

import os
import tempfile
from unittest.mock import patch

import pytest
from pydantic import ValidationError

from app.config import Settings


class TestSettings:
    """Test cases for the Settings class."""

    def test_default_values(self):
        """Test that default values are set correctly."""
        # Mock required environment variables and clear any existing ones
        with patch.dict(
            os.environ,
            {
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
            clear=True,
        ):
            settings = Settings()

            assert settings.APP_TITLE == "Playlist Intelligence Agent MVP - Dev"
            assert settings.API_VERSION == "0.1.0"
            assert settings.LOG_LEVEL == "INFO"
            assert settings.FRONTEND_URL == "http://localhost:5173"
            assert settings.APP_ENV == "development"
            assert settings.DB_USER == "admin"
            assert settings.DB_PASSWORD == "password"
            assert settings.DB_HOST == "localhost"
            assert settings.DB_PORT == 5432
            assert settings.DB_NAME == "playlist_agent_dev"
            assert settings.DB_ECHO is False
            assert settings.JWT_ALGORITHM == "HS256"
            assert settings.JWT_EXPIRY_MINUTES == 60

    def test_environment_variable_override(self):
        """Test that environment variables override default values."""
        with patch.dict(
            os.environ,
            {
                "APP_TITLE": "Custom Title",
                "API_VERSION": "1.0.0",
                "LOG_LEVEL": "DEBUG",
                "FRONTEND_URL": "http://custom-frontend.com",
                "APP_ENV": "production",
                "DB_USER": "custom_user",
                "DB_PASSWORD": "custom_password",
                "DB_HOST": "custom_host",
                "DB_PORT": "3306",
                "DB_NAME": "custom_db",
                "DB_ECHO": "true",
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
                "JWT_ALGORITHM": "RS256",
                "JWT_EXPIRY_MINUTES": "120",
            },
        ):
            settings = Settings()

            assert settings.APP_TITLE == "Custom Title"
            assert settings.API_VERSION == "1.0.0"
            assert settings.LOG_LEVEL == "DEBUG"
            assert settings.FRONTEND_URL == "http://custom-frontend.com"
            assert settings.APP_ENV == "production"
            assert settings.DB_USER == "custom_user"
            assert settings.DB_PASSWORD == "custom_password"
            assert settings.DB_HOST == "custom_host"
            assert settings.DB_PORT == 3306
            assert settings.DB_NAME == "custom_db"
            assert settings.DB_ECHO is True
            assert settings.JWT_ALGORITHM == "RS256"
            assert settings.JWT_EXPIRY_MINUTES == 120

    def test_required_spotify_settings_validation(self):
        """Test that required Spotify settings are validated."""
        # Test missing SPOTIFY_CLIENT_ID by setting it to empty string
        with patch.dict(
            os.environ,
            {
                "SPOTIFY_CLIENT_ID": "",  # Empty string should trigger validation
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "SPOTIFY_CLIENT_ID" in str(exc_info.value)

        # Test missing SPOTIFY_CLIENT_SECRET by setting it to empty string
        with patch.dict(
            os.environ,
            {
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "",  # Empty string should trigger validation
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "SPOTIFY_CLIENT_SECRET" in str(exc_info.value)

    def test_required_security_settings_validation(self):
        """Test that required security settings are validated."""
        # Test missing TOKENS_ENCRYPTION_KEY by setting it to empty string
        with patch.dict(
            os.environ,
            {
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "",  # Empty string should trigger validation
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "TOKENS_ENCRYPTION_KEY" in str(exc_info.value)

        # Test missing JWT_SECRET_KEY by setting it to empty string
        with patch.dict(
            os.environ,
            {
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "",  # Empty string should trigger validation
            },
        ):
            with pytest.raises(ValidationError) as exc_info:
                Settings()
            assert "JWT_SECRET_KEY" in str(exc_info.value)

    def test_database_url_generation(self):
        """Test that database URLs are generated correctly."""
        with patch.dict(
            os.environ,
            {
                "DB_USER": "testuser",
                "DB_PASSWORD": "testpass",
                "DB_HOST": "testhost",
                "DB_PORT": "5433",
                "DB_NAME": "testdb",
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            settings = Settings()

            expected_async_url = (
                "postgresql+asyncpg://testuser:testpass@testhost:5433/testdb"
            )
            expected_sync_url = (
                "postgresql+psycopg2://testuser:testpass@testhost:5433/testdb"
            )

            assert settings.DATABASE_URL_ASYNC == expected_async_url
            assert settings.DATABASE_URL_SYNC == expected_sync_url

    def test_env_file_loading(self):
        """Test that .env file is loaded correctly."""
        # Create a temporary .env file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".env", delete=False) as f:
            f.write("APP_TITLE=Test From Env File\n")
            f.write("LOG_LEVEL=WARNING\n")
            f.write("SPOTIFY_CLIENT_ID=env_client_id\n")
            f.write("SPOTIFY_CLIENT_SECRET=env_client_secret\n")
            f.write("SPOTIFY_REDIRECT_URI=http://localhost:8000/callback\n")
            f.write("TOKENS_ENCRYPTION_KEY=env_encryption_key\n")
            f.write("JWT_SECRET_KEY=env_jwt_secret\n")
            env_file_path = f.name

        try:
            # Test loading from the .env file
            # Test loading from the .env file by setting the environment
            with patch.dict(os.environ, {}, clear=True):
                # Manually load the env file content
                with open(env_file_path) as f:
                    for line in f:
                        if "=" in line and not line.startswith("#"):
                            key, value = line.strip().split("=", 1)
                            os.environ[key] = value.strip('"')

                settings = Settings()

            assert settings.APP_TITLE == "Test From Env File"
            assert settings.LOG_LEVEL == "WARNING"
            assert settings.SPOTIFY_CLIENT_ID == "env_client_id"
            assert settings.SPOTIFY_CLIENT_SECRET == "env_client_secret"
            assert settings.TOKENS_ENCRYPTION_KEY == "env_encryption_key"
            assert settings.JWT_SECRET_KEY == "env_jwt_secret"
        finally:
            # Clean up the temporary file
            os.unlink(env_file_path)

    def test_boolean_conversion(self):
        """Test that boolean values are converted correctly from environment variables."""
        with patch.dict(
            os.environ,
            {
                "DB_ECHO": "true",
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            settings = Settings()
            assert settings.DB_ECHO is True

        with patch.dict(
            os.environ,
            {
                "DB_ECHO": "false",
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            settings = Settings()
            assert settings.DB_ECHO is False

    def test_integer_conversion(self):
        """Test that integer values are converted correctly from environment variables."""
        with patch.dict(
            os.environ,
            {
                "DB_PORT": "3306",
                "JWT_EXPIRY_MINUTES": "240",
                "SPOTIFY_CLIENT_ID": "test_client_id",
                "SPOTIFY_CLIENT_SECRET": "test_client_secret",
                "SPOTIFY_REDIRECT_URI": "http://localhost:8000/callback",
                "TOKENS_ENCRYPTION_KEY": "test_encryption_key",
                "JWT_SECRET_KEY": "test_jwt_secret",
            },
        ):
            settings = Settings()
            assert settings.DB_PORT == 3306
            assert settings.JWT_EXPIRY_MINUTES == 240
