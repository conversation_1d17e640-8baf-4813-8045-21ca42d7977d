"""
Shared test configuration and fixtures.
"""
import os
import sys
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

# Add the src directory to the Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from app.main import app


@pytest.fixture
def test_client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    session = AsyncMock(spec=AsyncSession)
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.refresh = AsyncMock()
    session.close = AsyncMock()
    session.execute = AsyncMock()
    return session


@pytest.fixture
def mock_spotify_client():
    """Create a mock Spotify client."""
    client = MagicMock()
    client.current_user_playlists = MagicMock()
    client.playlist = MagicMock()
    client.playlist_items = MagicMock()
    client.current_user = MagicMock()
    return client


@pytest.fixture
def mock_spotify_oauth():
    """Create a mock Spotify OAuth manager."""
    oauth = MagicMock()
    oauth.get_authorize_url = MagicMock()
    oauth.get_access_token = MagicMock()
    oauth.refresh_access_token = MagicMock()
    return oauth


@pytest.fixture
def sample_spotify_playlist_response():
    """Sample Spotify playlist API response."""
    return {
        "items": [
            {
                "id": "playlist1",
                "name": "My Playlist 1",
                "tracks": {"total": 25}
            },
            {
                "id": "playlist2",
                "name": "My Playlist 2",
                "tracks": {"total": 50}
            }
        ],
        "next": None,
        "total": 2
    }


@pytest.fixture
def sample_spotify_playlist_tracks_response():
    """Sample Spotify playlist tracks API response."""
    return {
        "items": [
            {
                "track": {
                    "id": "track1",
                    "name": "Song 1",
                    "artists": [
                        {"id": "artist1", "name": "Artist 1"},
                        {"id": "artist2", "name": "Artist 2"}
                    ]
                }
            },
            {
                "track": {
                    "id": "track2",
                    "name": "Song 2",
                    "artists": [
                        {"id": "artist3", "name": "Artist 3"}
                    ]
                }
            }
        ],
        "next": None
    }


@pytest.fixture
def sample_spotify_playlist_info():
    """Sample Spotify playlist info response."""
    return {
        "id": "playlist1",
        "name": "Test Playlist",
        "snapshot_id": "snapshot123",
        "tracks": {"total": 2}
    }


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "spotify_user_id": "test_user_123",
        "spotify_display_name": "Test User",
        "access_token": "test_access_token",
        "refresh_token": "test_refresh_token"
    }


@pytest.fixture
def sample_spotify_token_info():
    """Sample Spotify token info response."""
    return {
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "expires_at": 1234567890,
        "token_type": "Bearer",
        "scope": "playlist-read-private playlist-read-collaborative"
    }
