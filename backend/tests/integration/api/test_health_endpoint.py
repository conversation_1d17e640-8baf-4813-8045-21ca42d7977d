"""
Integration tests for health check endpoints.
"""

import pytest
from fastapi.testclient import TestClient


@pytest.mark.integration
class TestHealthEndpoint:
    """Integration tests for health check endpoints."""

    def test_health_check_success(self, test_client: TestClient):
        """Test that health check endpoint returns success."""
        response = test_client.get("/api/v1/health")

        assert response.status_code == 200

        data = response.json()
        assert "status" in data
        assert data["status"] == "ok"

    def test_health_check_response_format(self, test_client: TestClient):
        """Test that health check response has the correct format."""
        response = test_client.get("/api/v1/health")

        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

        data = response.json()

        # Verify required fields
        required_fields = ["status"]
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"

        # Verify field types
        assert isinstance(data["status"], str)

        # Verify status value
        assert data["status"] == "ok"

    def test_health_check_multiple_requests(self, test_client: TestClient):
        """Test that health check endpoint handles multiple requests correctly."""
        # Make multiple requests
        responses = []
        for _ in range(5):
            response = test_client.get("/api/v1/health")
            responses.append(response)

        # All requests should succeed
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "ok"

    def test_health_check_cors_headers(self, test_client: TestClient):
        """Test that health check endpoint includes proper CORS headers."""
        response = test_client.get("/api/v1/health")

        assert response.status_code == 200

        # Check for CORS headers (if configured)
        # Note: This depends on the CORS configuration in the FastAPI app
        headers = response.headers

        # At least verify the response is successful and properly formatted
        assert "content-type" in headers
        assert headers["content-type"] == "application/json"

    def test_health_check_options_request(self, test_client: TestClient):
        """Test OPTIONS request to health check endpoint for CORS preflight."""
        response = test_client.options("/api/v1/health")

        # OPTIONS request should be handled (status depends on CORS configuration)
        # At minimum, it shouldn't return a 404 or 500
        assert response.status_code in [
            200,
            204,
            405,
        ]  # 405 is acceptable if OPTIONS not explicitly handled

    def test_health_check_with_query_parameters(self, test_client: TestClient):
        """Test health check endpoint with query parameters (should be ignored)."""
        response = test_client.get("/api/v1/health?param=value&other=test")

        assert response.status_code == 200

        data = response.json()
        assert data["status"] == "ok"
        # Query parameters should not affect the response

    def test_health_check_with_headers(self, test_client: TestClient):
        """Test health check endpoint with custom headers."""
        headers = {
            "User-Agent": "Test Client",
            "Accept": "application/json",
            "Custom-Header": "test-value",
        }

        response = test_client.get("/api/v1/health", headers=headers)

        assert response.status_code == 200

        data = response.json()
        assert data["status"] == "ok"

    def test_health_check_response_time(self, test_client: TestClient):
        """Test that health check endpoint responds quickly."""
        import time

        start_time = time.time()
        response = test_client.get("/api/v1/health")
        end_time = time.time()

        response_time = end_time - start_time

        assert response.status_code == 200
        # Health check should respond within 1 second
        assert response_time < 1.0, f"Health check took too long: {response_time:.3f}s"

    def test_health_check_concurrent_requests(self, test_client: TestClient):
        """Test health check endpoint with concurrent requests."""
        import concurrent.futures

        def make_request():
            return test_client.get("/api/v1/health")

        # Make 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            responses = [
                future.result() for future in concurrent.futures.as_completed(futures)
            ]

        # All requests should succeed
        for response in responses:
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "ok"

    def test_health_check_invalid_method(self, test_client: TestClient):
        """Test health check endpoint with invalid HTTP methods."""
        # Test POST (should not be allowed)
        response = test_client.post("/api/v1/health")
        assert response.status_code == 405  # Method Not Allowed

        # Test PUT (should not be allowed)
        response = test_client.put("/api/v1/health")
        assert response.status_code == 405  # Method Not Allowed

        # Test DELETE (should not be allowed)
        response = test_client.delete("/api/v1/health")
        assert response.status_code == 405  # Method Not Allowed

    def test_health_check_simple_get(self, test_client: TestClient):
        """Test simple GET request to health check endpoint."""
        response = test_client.get("/api/v1/health")

        assert response.status_code == 200

        data = response.json()
        assert data["status"] == "ok"
